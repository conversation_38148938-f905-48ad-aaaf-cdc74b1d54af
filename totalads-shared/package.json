{"name": "totalads-shared", "license": "MIT", "version": "0.0.1", "files": ["dist", "package.json", ".env.local", ".env"], "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"prepare": "tsup src/*", "build": "tsup src/*", "release": "pnpm run build && changeset publish", "lint": "tsc", "migration:update": "drizzle-kit up", "migration:generate": "drizzle-kit generate", "migration:drop": "drizzle-kit drop", "migration:check": "drizzle-kit check", "migration:migrate": "drizzle-kit migrate", "drizzle:studio": "drizzle-kit studio"}, "keywords": [], "author": "", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/cloudfront-signer": "^3.734.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@node-rs/bcrypt": "^1.10.7", "async-retry": "^1.3.3", "axios": "^1.7.9", "bullmq": "^5.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "drizzle-orm": "^0.40.0", "express": "^4.21.2", "http-status-codes": "^2.3.0", "ioredis": "^5.5.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.28", "morgan": "^1.10.0", "pg": "^8.13.3", "phone": "^3.1.58", "postgres": "^3.4.5", "resend": "^4.1.2", "swagger-autogen": "^2.23.7", "swagger-ui-express": "^5.0.1", "tsc": "^2.0.4", "tsup": "^8.4.0", "typescript": "^5.8.2", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@changesets/cli": "^2.28.1", "@types/async-retry": "^1.4.9", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.8", "@types/morgan": "^1.9.9", "@types/pg": "^8.11.11", "@types/swagger-ui-express": "^4.1.8", "drizzle-kit": "^0.30.4", "tsx": "^4.19.3"}, "lint-staged": {"**/*": ["eslint . --fix", "prettier --write --ignore-unknown ."]}}