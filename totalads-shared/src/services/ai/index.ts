// Export types
export {
  AboutInfo,
  CompanyInfo,
  TeamMember,
  DataType,
  SummarizationConfig,
  DEFAULT_SUMMARIZATION_CONFIG,
  SummarizationResult,
  TextSummarizerInput,
  TextSummarizerOutput,
  AboutDataEnhancerInput,
  EnhancedAboutInfo
} from './types';

// Export utility functions for caching
export { globalSummarizationCache } from './cache';
export { generateInputHash } from './utils';

// Export Gemini client functions
export { createGeminiClient, completeWithGemini } from './geminiClient';

// Export new AI functions
export { summarizeText } from './textSummarizer';
export { enhanceAboutData } from './aboutDataEnhancer';
