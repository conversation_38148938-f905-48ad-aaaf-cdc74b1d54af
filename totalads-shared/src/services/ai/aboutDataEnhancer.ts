import { SummarizationConfig, DEFAULT_SUMMARIZATION_CONFIG, SummarizationResult, AboutInfo } from './types';
import { completeWithGemini } from './geminiClient';

/**
 * Enhanced about information with AI enrichment
 */
export interface EnhancedAboutInfo extends AboutInfo {
  // Additional fields that might be extracted by AI
  summary?: string;
  keyInsights?: string[];
  businessModel?: string;
  targetAudience?: string;
  competitors?: string[];
  uniqueSellingPropositions?: string[];
  foundingYear?: number;
}

/**
 * Input for the about data enhancer
 */
export interface AboutDataEnhancerInput {
  aboutData: AboutInfo;
  websiteUrl?: string; // Optional website URL for context
  additionalContext?: string; // Any other context that might help the AI
}

/**
 * Analyzes and enhances company about data, filling in missing details
 * @param input - About data and optional context
 * @param config - Configuration for AI processing
 * @returns Enhanced about information with missing details filled
 */
export async function enhanceAboutData(
  input: AboutDataEnhancerInput,
  config: SummarizationConfig = DEFAULT_SUMMARIZATION_CONFIG
): Promise<SummarizationResult<EnhancedAboutInfo>> {
  try {
    // Handle empty input
    if (!input.aboutData) {
      return {
        success: false,
        error: 'No about data provided'
      };
    }

    // Create client
    const client = { apiKey: process.env.GEMINI_API_KEY || '' };

    // Convert the about data to a JSON string for the prompt
    const aboutDataJson = JSON.stringify(input.aboutData, null, 2);

    // Prepare the prompt for Gemini
    const prompt = `
You are a business intelligence expert specializing in extracting comprehensive company information from partial data.

INPUT JSON ABOUT DATA:
${aboutDataJson}

${input.websiteUrl ? `COMPANY WEBSITE: ${input.websiteUrl}` : ''}

${input.additionalContext ? `ADDITIONAL CONTEXT: ${input.additionalContext}` : ''}

TASK:
1. Analyze the provided JSON data about a company.
2. Fill in any missing fields based on the available information.
3. Extract additional business insights not explicitly stated.
4. Create a concise summary of the company.

For example:
- From company description, infer industry types, company values, and business model
- From founding information, extract founding year and growth trajectory
- From mission statement, identify target audience and unique selling propositions

OUTPUT FORMAT:
Create a complete JSON object that includes:
1. All original fields from the input (keeping existing values)
2. Missing fields filled with inferred values where possible
3. Additional fields:
   - summary: A concise 3-4 line summary of the company
   - keyInsights: Array of important business insights
   - businessModel: Description of how the company generates revenue
   - targetAudience: Who the company serves
   - competitors: Potential competitors based on industry
   - uniqueSellingPropositions: What makes the company stand out
   - foundingYear: Numeric year of founding (if can be inferred)

Ensure the output is valid JSON and maintain data types consistent with the input.
`.trim();

    // Call Gemini with context caching
    const result = await completeWithGemini(client, prompt, {
      ...config,
      temperature: 0.2, // Lower temperature for more factual responses
      enableCaching: true,
    });

    // Parse the result - assuming proper JSON response from Gemini
    try {
      // Clean the response text in case it contains markdown code blocks or other formatting
      const cleanedText = result.text
        .replace(/```json/gi, '')
        .replace(/```/g, '')
        .trim();
      
      const parsedResult = JSON.parse(cleanedText) as EnhancedAboutInfo;
      
      return {
        success: true,
        data: parsedResult,
        tokensUsed: result.tokensUsed,
        requestTime: result.requestTime,
        fromCache: result.fromCache
      };
    } catch (parseError) {
      console.error('Failed to parse Gemini response as JSON:', parseError);
      console.log('Raw response:', result.text);
      
      // Return original data with error
      return {
        success: false,
        data: input.aboutData,
        error: `Failed to parse enhanced data: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`,
        tokensUsed: result.tokensUsed,
        requestTime: result.requestTime
      };
    }
    
  } catch (error) {
    console.error('Error in enhanceAboutData:', error);
    return {
      success: false,
      error: `About data enhancement failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
