// This is a compatibility layer to ensure exports are properly exposed
import { summarizeText } from './textSummarizer';
import { enhanceAboutData, EnhancedAboutInfo, AboutDataEnhancerInput } from './aboutDataEnhancer';
import { AboutInfo, SummarizationConfig, DEFAULT_SUMMARIZATION_CONFIG, SummarizationResult } from './types';
import { completeWithGemini, createGeminiClient } from './geminiClient';
import { globalSummarizationCache } from './cache';
import { generateInputHash } from './utils';

// Re-export everything
export {
  // AI client functions
  completeWithGemini,
  createGeminiClient,
  
  // AI utility functions 
  summarizeText,
  enhanceAboutData,
  
  // Cache and utilities
  globalSummarizationCache,
  generateInputHash,
  
  // Types
  AboutInfo,
  EnhancedAboutInfo,
  AboutDataEnhancerInput,
  SummarizationConfig,
  DEFAULT_SUMMARIZATION_CONFIG,
  SummarizationResult
};
