import { CacheEntry } from './types';
import { generateInputHash } from './utils';

/**
 * Simple in-memory cache for AI responses to minimize API calls
 */
export class SummarizationCache {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private readonly maxCacheSize: number;
  private readonly cacheTtlMs: number;

  /**
   * Creates a new instance of the summarization cache
   * @param maxSize - Maximum number of items in cache (default 100)
   * @param ttlHours - Time-to-live in hours (default 24)
   */
  constructor(maxSize = 100, ttlHours = 24) {
    this.maxCacheSize = maxSize;
    this.cacheTtlMs = ttlHours * 60 * 60 * 1000;
  }

  /**
   * Retrieves a cached result if available and not expired
   * @param input - Input data
   * @returns Cached result or undefined if not found/expired
   */
  get<T>(input: any): T | undefined {
    const hash = generateInputHash(input);
    const entry = this.cache.get(hash);

    if (!entry) {
      return undefined;
    }

    // Check if entry has expired
    const now = Date.now();
    if (now - entry.timestamp > this.cacheTtlMs) {
      this.cache.delete(hash);
      return undefined;
    }

    return entry.result as T;
  }

  /**
   * Stores a result in the cache
   * @param input - Input data
   * @param result - Result to cache
   */
  set<T>(input: any, result: T): void {
    const hash = generateInputHash(input);

    // If cache is at max size, remove oldest entry
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = this.findOldestCacheKey();
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(hash, {
      timestamp: Date.now(),
      result,
      inputHash: hash,
    });
  }

  /**
   * Clears the entire cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Gets the current cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Finds the oldest entry in the cache
   * @returns Key of the oldest cache entry
   */
  private findOldestCacheKey(): string | undefined {
    let oldestKey: string | undefined;
    let oldestTimestamp = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }
}

// Create a singleton instance for global use
export const globalSummarizationCache = new SummarizationCache();
