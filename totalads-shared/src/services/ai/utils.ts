import { createHash } from "crypto";

/**
 * Generates a hash for caching purposes
 * @param data - Data to hash
 * @returns Hash string
 */
export function generateInputHash(data: any): string {
	const stringData = typeof data === "string" ? data : JSON.stringify(data);
	return createHash("sha256").update(stringData).digest("hex");
}

/**
 * Hash a string to create a deterministic key for caching
 * @param str String to hash
 * @returns Hashed string
 */
export function hashString(str: string): string {
	let hash = 0;
	for (let i = 0; i < str.length; i++) {
		const char = str.charCodeAt(i);
		hash = (hash << 5) - hash + char;
		hash = hash & hash; // Convert to 32bit integer
	}
	return hash.toString(16);
}

/**
 * Create a cache key for storing and retrieving results
 * @param prefix Prefix to identify the type of data
 * @param data String representation of data to hash
 * @returns Cache key
 */
export function createCacheKey(prefix: string, data: string): string {
	return `${prefix}_${hashString(data)}`;
}

/**
 * Truncates text to a specified token count (approximate)
 * @param text - Text to truncate
 * @param maxTokens - Maximum token count
 * @returns Truncated text
 */
export function truncateToTokenLimit(text: string, maxTokens: number): string {
	// Rough approximation: 1 token ≈ 4 characters for English
	const approximateCharCount = maxTokens * 4;

	if (text.length <= approximateCharCount) {
		return text;
	}

	// Truncate and add indicator
	return text.substring(0, approximateCharCount - 3) + "...";
}

/**
 * Splits text into chunks that won't exceed token limits
 * @param text - Text to split
 * @param maxTokens - Maximum tokens per chunk
 * @returns Array of text chunks
 */
export function splitTextIntoChunks(text: string, maxTokens: number): string[] {
	const chunks: string[] = [];
	const approximateCharCount = maxTokens * 4;

	// Split by paragraphs first
	const paragraphs = text.split("\n\n");
	let currentChunk = "";

	for (const paragraph of paragraphs) {
		// If adding this paragraph would exceed the limit, save current chunk and start a new one
		if ((currentChunk + paragraph).length > approximateCharCount) {
			if (currentChunk) {
				chunks.push(currentChunk);
				currentChunk = "";
			}

			// Handle paragraphs that are too long by themselves
			if (paragraph.length > approximateCharCount) {
				const sentenceChunks = splitLongParagraph(
					paragraph,
					approximateCharCount
				);
				chunks.push(...sentenceChunks.slice(0, -1));
				currentChunk = sentenceChunks[sentenceChunks.length - 1] || "";
			} else {
				currentChunk = paragraph;
			}
		} else {
			// Add separator if not the first paragraph in this chunk
			if (currentChunk) {
				currentChunk += "\n\n";
			}
			currentChunk += paragraph;
		}
	}

	// Don't forget the last chunk
	if (currentChunk) {
		chunks.push(currentChunk);
	}

	return chunks;
}

/**
 * Helper function to split long paragraphs by sentences
 * @param paragraph - Long paragraph to split
 * @param maxChars - Maximum characters per chunk
 * @returns Array of text chunks
 */
function splitLongParagraph(paragraph: string, maxChars: number): string[] {
	const sentences = paragraph.match(/[^.!?]+[.!?]+/g) || [paragraph];
	const chunks: string[] = [];
	let currentChunk = "";

	for (const sentence of sentences) {
		if ((currentChunk + sentence).length > maxChars) {
			if (currentChunk) {
				chunks.push(currentChunk);
				currentChunk = "";
			}

			// If a single sentence is too long, split by words
			if (sentence.length > maxChars) {
				const words = sentence.split(" ");
				for (const word of words) {
					if ((currentChunk + " " + word).length > maxChars) {
						chunks.push(currentChunk);
						currentChunk = word;
					} else {
						currentChunk += (currentChunk ? " " : "") + word;
					}
				}
			} else {
				currentChunk = sentence;
			}
		} else {
			currentChunk += (currentChunk ? " " : "") + sentence;
		}
	}

	if (currentChunk) {
		chunks.push(currentChunk);
	}

	return chunks;
}

/**
 * Calculates an estimated token count for a given text
 * @param text - Text to estimate tokens for
 * @returns Estimated token count
 */
export function estimateTokenCount(text: string): number {
	// Rough approximation: 1 token ≈ 4 characters for English text
	return Math.ceil(text.length / 4);
}

/**
 * Generates a standardized prompt template for data extraction
 * @param context - The text to analyze
 * @param task - Description of the extraction task
 * @param format - Output format instructions
 * @returns Formatted prompt
 */
export function generateExtractionPrompt(
	context: string,
	task: string,
	format: string
): string {
	return `
I need you to analyze the following information and ${task}.

CONTEXT:
${context}

OUTPUT INSTRUCTIONS:
${format}

Be concise and factual. Only include information that can be directly inferred from the provided context.
`.trim();
}

/**
 * Formats a summary or extracted information for better readability
 * @param text - Raw extracted text
 * @returns Formatted text
 */
export function formatOutput(text: string): string {
	return text
		.trim()
		.replace(/\n{3,}/g, "\n\n") // Replace excess newlines
		.replace(/^\s*[-*•]\s*/gm, "• "); // Standardize bullet points
}
