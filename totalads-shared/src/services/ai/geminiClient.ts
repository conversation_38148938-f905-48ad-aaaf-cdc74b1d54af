import {
	GoogleGenerativeAI,
	HarmBlockThreshold,
	HarmCategory,
} from "@google/generative-ai";

import { globalSummarizationCache } from "./cache";
import { DEFAULT_SUMMARIZATION_CONFIG, SummarizationConfig } from "./types";
import { generateInputHash } from "./utils";

// Type definition for Gemini API response
interface GeminiResponse {
	text: string;
	tokensUsed: number;
}

/**
 * Creates a Gemini client for API access
 * @param apiKey - Gemini API key
 * @returns Configured client object
 */
export function createGeminiClient(apiKey?: string): { apiKey: string } {
	const key = apiKey || process.env.GEMINI_API_KEY;

	if (!key) {
		throw new Error(
			"Gemini API key is required. Set GEMINI_API_KEY environment variable or pass it explicitly."
		);
	}

	return {
		apiKey: key,
	};
}

/**
 * Completes a prompt using Gemini Flash model with context caching
 * @param client - Gemini client instance
 * @param prompt - Text prompt to complete
 * @param config - Configuration options
 * @returns Generated text response
 */
export async function completeWithGemini(
	client: { apiKey: string },
	prompt: string,
	config: SummarizationConfig = DEFAULT_SUMMARIZATION_CONFIG
): Promise<{
	text: string;
	tokensUsed: number;
	requestTime: number;
	fromCache?: boolean;
}> {
	// Check cache first if enabled
	if (config.enableCaching) {
		const cacheKey = {
			prompt,
			temperature: config.temperature || 0.2,
			maxTokens: config.maxTokensPerCall || 1024,
		};

		const cachedResult = globalSummarizationCache.get<{
			text: string;
			tokensUsed: number;
			requestTime: number;
		}>(cacheKey);

		if (cachedResult) {
			console.log(
				"Using cached Gemini response for prompt:",
				generateInputHash(prompt).slice(0, 8)
			);
			return {
				...cachedResult,
				fromCache: true,
			};
		}
	}

	const startTime = Date.now();

	try {
		// Call the actual Gemini API
		const response = await callGeminiApi(client.apiKey, prompt, {
			model: "gemini-1.5-flash",
			maxTokens: config.maxTokensPerCall || 1024,
			temperature: config.temperature || 0.2,
		});

		const endTime = Date.now();
		const result = {
			text: response.text,
			tokensUsed: response.tokensUsed,
			requestTime: endTime - startTime,
		};

		// Store in cache if enabled
		if (config.enableCaching) {
			const cacheKey = {
				prompt,
				temperature: config.temperature || 0.2,
				maxTokens: config.maxTokensPerCall || 1024,
			};
			globalSummarizationCache.set(cacheKey, result);
		}

		return result;
	} catch (error) {
		console.error("Error calling Gemini API:", error);
		throw new Error(
			`Gemini API error: ${
				error instanceof Error ? error.message : "Unknown error"
			}`
		);
	}
}

/**
 * Helper function to call the Gemini API using the official Google Generative AI SDK
 */
async function callGeminiApi(
	apiKey: string,
	prompt: string,
	options: { model: string; maxTokens: number; temperature: number }
): Promise<GeminiResponse> {
	// Initialize the Google Generative AI client
	const genAI = new GoogleGenerativeAI(apiKey);

	// Get the model
	const model = genAI.getGenerativeModel({
		model: options.model,
		generationConfig: {
			temperature: options.temperature,
			maxOutputTokens: options.maxTokens,
			topP: 0.95,
			topK: 64,
		},
		safetySettings: [
			{
				category: HarmCategory.HARM_CATEGORY_HARASSMENT,
				threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
			},
			{
				category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
				threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
			},
			{
				category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
				threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
			},
			{
				category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
				threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
			},
		],
	});

	try {
		// Generate content
		const result = await model.generateContent(prompt);
		const response = await result.response;
		const text = response.text();

		// Get usage metadata if available
		const usageMetadata = result.response.usageMetadata;
		const tokensUsed = usageMetadata?.totalTokenCount || 0;

		return {
			text,
			tokensUsed,
		};
	} catch (error) {
		console.error("Error calling Gemini API:", error);
		throw new Error(
			`Gemini API error: ${
				error instanceof Error ? error.message : "Unknown error"
			}`
		);
	}
}
