/**
 * AI Service Wrapper for CommonJS and ESM compatibility
 * This file provides direct implementations using Gemini Flash
 */

import {
	AboutDataEnhancerInput,
	enhanceAboutData as enhanceAboutDataImpl,
	EnhancedAboutInfo,
} from "./aboutDataEnhancer";
import {
	summarizeText as summarizeTextImpl,
	TextSummarizerInput,
} from "./textSummarizer";
import { SummarizationResult, TextSummarizerOutput } from "./types";

// Re-export types for convenience
export type {
	TextSummarizerInput,
	TextSummarizerOutput,
	AboutDataEnhancerInput,
	EnhancedAboutInfo,
	SummarizationResult,
};

// Re-export the actual implementations
export async function summarizeText(
	input: TextSummarizerInput
): Promise<SummarizationResult<TextSummarizerOutput>> {
	try {
		return await summarizeTextImpl(input);
	} catch (error) {
		console.error("Error in summarizeText wrapper:", error);
		// Return fallback response
		return {
			success: false,
			error: `Summarization failed: ${
				error instanceof Error ? error.message : "Unknown error"
			}`,
		};
	}
}

export async function enhanceAboutData(
	input: AboutDataEnhancerInput
): Promise<SummarizationResult<EnhancedAboutInfo>> {
	try {
		return await enhanceAboutDataImpl(input);
	} catch (error) {
		console.error("Error in enhanceAboutData wrapper:", error);
		// Return fallback response
		return {
			success: false,
			error: `Enhancement failed: ${
				error instanceof Error ? error.message : "Unknown error"
			}`,
		};
	}
}
