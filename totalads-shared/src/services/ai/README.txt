# AI Summarization Service

This module provides AI-powered summarization and information extraction capabilities for TotalAds. It uses Google's Gemini Flash model to efficiently summarize company information, team members, and other business data with **optimized batch processing to minimize API calls** while minimizing API credit usage.

## Features

- **Efficient Data Summarization**: Extract key insights from company information and team data
- **Credit Optimization**: Built-in techniques to reduce Gemini API usage
  - Caching of results to avoid redundant calls
  - Smart text truncation to limit token usage
  - Optional batch processing for related requests
  - Configurable detail levels for different use cases
- **Flexible Integration**: Works seamlessly with existing extractors and modules
- **TypeScript Support**: Full type definitions for safe integration

## Installation

The service is integrated into the `totalads-shared` module. To use it in another module:

```bash
# Add the required dependencies to your project
pnpm add @google/generative-ai
```

## API Key Setup

You need a Google Gemini API key to use this service. Add it to your `.env` file:

```
GEMINI_API_KEY=your_api_key_here
```

## Basic Usage

Import the AI functions in your code:

```typescript
import { summarizeText, enhanceAboutData } from 'totalads-shared';
```

Use the functions:

```typescript
// Summarize text content
const summaryResult = await summarizeText({
  text: "Your long text content here..."
});

if (summaryResult.success) {
  console.log("Company Summary:", result.data);
  console.log(`Used ${result.tokensUsed} tokens`);
} else {
  console.error("Error:", result.error);
}
```

## Integration with Extractors

The service can be used to enhance the data extracted from web pages. Here's how to integrate it with your existing extractors:

```typescript
import { enhanceAboutData, summarizeText, AboutInfo } from 'totalads-shared';

// Inside your extractor function
async function extractCompanyInfoFromSite(page, initialUrl, config) {
  // ... your existing extraction logic

  // Extract basic info using Puppeteer
  const extractedInfo = {
    companyDescription: '...',
    // ... other fields
  };

  // Use AI to enhance data if needed
  const needsEnhancement = !extractedInfo.industries || extractedInfo.industries.length === 0;

  if (needsEnhancement && extractedInfo.companyDescription) {
    try {
      // Use the AI to extract missing information
      const enhancedInfo = await enhanceAboutData({
        aboutData: extractedInfo,
        websiteUrl: page.url()
      });

      if (enhancedInfo.success && enhancedInfo.data) {
        // Merge the enhanced data with your extracted data
        return enhancedInfo.data;
      }
    } catch (error) {
      console.error("Error enhancing company info:", error);
    }
  }

  return extractedInfo;
}
```

## Advanced Configuration

You can customize the behavior of the AI functions through configuration:

```typescript
import { summarizeText, DEFAULT_SUMMARIZATION_CONFIG } from 'totalads-shared';

// Custom configuration
const customConfig = {
  // Maximum tokens to process per API call
  maxTokensPerCall: 2000,

  // Temperature setting (0.0-1.0, lower = more deterministic)
  temperature: 0.2,

  // Timeout for API calls in milliseconds
  timeoutMs: 30000,

  // Whether to cache results to avoid redundant API calls
  enableCaching: true
};

// Use with custom configuration
const result = await summarizeText({
  text: "Your text here..."
}, customConfig);
```

## Best Practices for Minimizing Gemini API Credits

1. **Minimize token usage** by only sending necessary text
2. Check for existing data before using AI summarization
3. Set appropriate `maxTokensPerCall` limits for your use case
4. Use conditional checks before invoking summarization
5. Leverage the caching system for repeated requests
6. Use context caching for similar prompts

## Usage Examples

### Text Summarization

```typescript
import { summarizeText } from 'totalads-shared';

// Summarize text content
const result = await summarizeText({
  text: "Your long text content here...",
  maxLength: 100, // Optional: limit summary length
  focusPoints: ["business model", "key features"] // Optional: focus areas
});

if (result.success) {
  console.log("Summary:", result.data.summary);
  console.log("Key Points:", result.data.keyPoints);
  console.log("Business Info:", result.data.businessRelevance);
}
```

### About Data Enhancement

```typescript
import { enhanceAboutData } from 'totalads-shared';

// Enhance company about data
const aboutData = {
  companyDescription: 'A tech company that builds innovative solutions...',
  // Other fields may be missing and will be filled by AI
};

const enhancementResult = await enhanceAboutData({
  aboutData: aboutData,
  websiteUrl: 'https://example.com',
  additionalContext: 'Additional context about the company'
});

if (enhancementResult.success) {
  console.log('Enhanced company info:', enhancementResult.data);
  console.log('AI-generated summary:', enhancementResult.data.summary);
  console.log('Key insights:', enhancementResult.data.keyInsights);
  console.log(`Tokens used: ${enhancementResult.tokensUsed}`);
}
```

## Available Functions

### Text Summarization

- `summarizeText(input, config?)`: Summarize long text into concise 4-5 line summary with key business information
  - Input: `{ text: string, maxLength?: number, focusPoints?: string[] }`
  - Returns: `{ summary: string, keyPoints: string[], businessRelevance?: string }`

### About Data Enhancement

- `enhanceAboutData(input, config?)`: Analyze and enhance company about data, filling in missing details
  - Input: `{ aboutData: AboutInfo, websiteUrl?: string, additionalContext?: string }`
  - Returns: Enhanced AboutInfo with AI-generated insights

### Utility Functions

- `createGeminiClient(apiKey?)`: Create a Gemini API client
- `completeWithGemini(client, prompt, config?)`: Direct access to Gemini API with caching

### Cache Management

- `globalSummarizationCache`: Access to the global cache instance
- `generateInputHash(input)`: Generate cache keys for inputs

## Notes

All functions support context caching and are optimized for minimal token usage with Gemini Flash model.
