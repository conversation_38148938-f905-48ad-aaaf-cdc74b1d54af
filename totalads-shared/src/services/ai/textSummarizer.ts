import { SummarizationConfig, DEFAULT_SUMMARIZATION_CONFIG, SummarizationResult } from './types';
import { completeWithGemini } from './geminiClient';

/**
 * Input type for the text summarizer
 */
export interface TextSummarizerInput {
  text: string;
  maxLength?: number; // Optional max length in words
  focusPoints?: string[]; // Optional specific aspects to focus on
}

/**
 * Output type for the text summarizer
 */
export interface TextSummarizerOutput {
  summary: string;
  keyPoints: string[];
  businessRelevance?: string; // Important business information extracted
}

/**
 * Summarizes long text into a concise 4-5 line summary with key business information
 * @param input - Text to summarize and optional parameters
 * @param config - Configuration for AI processing
 * @returns Summary and extracted key business information
 */
export async function summarizeText(
  input: TextSummarizerInput,
  config: SummarizationConfig = DEFAULT_SUMMARIZATION_CONFIG
): Promise<SummarizationResult<TextSummarizerOutput>> {
  try {
    // Handle empty or invalid input
    if (!input.text || input.text.trim().length < 50) {
      return {
        success: false,
        error: 'Input text is too short or empty'
      };
    }

    // Create client
    const client = { apiKey: process.env.GEMINI_API_KEY || '' };

    // Prepare the prompt for Gemini
    const prompt = `
You are a business data analyst specialized in extracting and summarizing critical information from company data.

INPUT TEXT:
${input.text}

TASK:
1. Summarize the above text in 4-5 concise lines. Focus on the most important facts and information.
2. Extract 3-5 key points from the text.
3. Identify any important business information such as:
   - Industry specifics
   - Value propositions
   - Market positioning
   - Business model details
   - Competitive advantages
   - Financial insights (if present)
   - Strategic partnerships or relationships
   - Growth metrics or indicators

FORMAT YOUR RESPONSE AS JSON with these fields:
- summary: The 4-5 line summary
- keyPoints: Array of key points extracted
- businessRelevance: Important business information

Your output should be strictly JSON without any additional text or explanations.
`.trim();

    // Call Gemini with context caching
    const result = await completeWithGemini(client, prompt, {
      ...config,
      enableCaching: true,
    });

    // Parse the result - assuming proper JSON response from Gemini
    try {
      // Clean the response text in case it contains markdown code blocks or other formatting
      const cleanedText = result.text
        .replace(/```json/gi, '')
        .replace(/```/g, '')
        .trim();
      
      const parsedResult = JSON.parse(cleanedText) as TextSummarizerOutput;
      
      return {
        success: true,
        data: parsedResult,
        tokensUsed: result.tokensUsed,
        requestTime: result.requestTime,
        fromCache: result.fromCache
      };
    } catch (parseError) {
      console.error('Failed to parse Gemini response as JSON:', parseError);
      console.log('Raw response:', result.text);
      
      // Attempt a fallback approach to extract information
      const summary = result.text.substring(0, 500).trim();
      
      return {
        success: true,
        data: {
          summary: summary,
          keyPoints: ['Failed to parse structured response'],
          businessRelevance: 'Could not extract structured business information'
        },
        tokensUsed: result.tokensUsed,
        requestTime: result.requestTime,
        error: 'Response parsing failed, returning raw text',
        fromCache: result.fromCache
      };
    }
    
  } catch (error) {
    console.error('Error in summarizeText:', error);
    return {
      success: false,
      error: `Summarization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
