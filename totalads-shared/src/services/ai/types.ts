// Import necessary types
// Will be defined here since we don't have direct access to the scrapper types in shared module

// Simplified version of the types needed for summarization and enhancement
export interface TeamMember {
	name: string;
	title?: string;
	bio?: string;
	imageUrl?: string;
	contact?: {
		email?: string;
		phone?: string;
		linkedin?: string;
	};
}

export interface AboutInfo {
	companyDescription?: string;
	foundingInfo?: string;
	missionStatement?: string;
	companyValues?: string[];
	awards?: string[];
	industries?: string[];
	globalPresence?: boolean;
	officeLocations?: string[];
	certifications?: string[];
}

export interface CompanyInfo extends AboutInfo {
	teamMembers?: TeamMember[];
}

// Configuration options for the AI summarization
export interface SummarizationConfig {
	// Maximum tokens to process per API call (helps control costs)
	maxTokensPerCall?: number;
	// Temperature setting for Gemini (0.0-1.0, lower = more deterministic)
	temperature?: number;
	// Timeout for API calls in milliseconds
	timeoutMs?: number;
	// Whether to cache results to avoid redundant API calls
	enableCaching?: boolean;
}

// Default configuration
export const DEFAULT_SUMMARIZATION_CONFIG: SummarizationConfig = {
	maxTokensPerCall: 2000,
	temperature: 0.2,
	timeoutMs: 30000,
	enableCaching: true,
};

// Types of data that can be summarized
export enum DataType {
	COMPANY_DESCRIPTION = "company_description",
	MISSION_STATEMENT = "mission_statement",
	TEAM_MEMBERS = "team_members",
	COMPANY_VALUES = "company_values",
	INDUSTRIES = "industries",
	TEXT_SUMMARY = "text_summary",
	ABOUT_DATA_ENHANCEMENT = "about_data_enhancement",
}

// Result of a summarization operation
export interface SummarizationResult<T> {
	success: boolean;
	data?: T;
	error?: string;
	tokensUsed?: number;
	requestTime?: number;
	fromCache?: boolean;
}

// CompanyInfo summarization result
export interface CompanyInfoSummary {
	shortDescription?: string;
	keyFacts?: string[];
	industryFocus?: string[];
	highlights?: string;
}

// TeamMember summarization result
export interface TeamMemberSummary {
	key?: string; // Unique identifier (e.g., name)
	role?: string;
	expertise?: string[];
	summary?: string;
}

// Cache entry structure
export interface CacheEntry<T> {
	timestamp: number;
	result: T;
	inputHash: string;
}

// Specific types for different summarization functions
export type CompanyDescriptionInput = {
	text: string;
	existingDescription?: string;
	wordLimit?: number;
};

export type TeamMembersInput = {
	members: TeamMember[];
	focusAreas?: string[];
	detailLevel?: "minimal" | "standard" | "detailed";
};

export type CompanyValuesInput = {
	text: string;
	existingValues?: string[];
	maxValues?: number;
};

// Text summarizer types
export interface TextSummarizerInput {
	text: string;
	maxLength?: number; // Optional max length in words
	focusPoints?: string[]; // Optional specific aspects to focus on
}

export interface TextSummarizerOutput {
	summary: string;
	keyPoints: string[];
	businessRelevance?: string; // Important business information extracted
}

// About data enhancer types
export interface AboutDataEnhancerInput {
	aboutData: AboutInfo;
	websiteUrl?: string; // Optional website URL for context
	additionalContext?: string; // Any other context that might help the AI
}

export interface EnhancedAboutInfo extends AboutInfo {
	// Additional fields that might be extracted by AI
	summary?: string;
	keyInsights?: string[];
	businessModel?: string;
	targetAudience?: string;
	competitors?: string[];
	uniqueSellingPropositions?: string[];
	foundingYear?: number;
}
