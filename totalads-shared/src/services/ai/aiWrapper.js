/**
 * AI Service Wrapper for CommonJS and ESM compatibility
 * This file works around ESM/CommonJS compatibility issues by providing direct implementations
 */

// Mock implementation of summarizeText that matches the interface
async function summarizeText(input) {
  console.log("AI Summary called with:", input ? input.text?.substring(0, 100) + '...' : 'No input');
  
  // Return a mock result with dummy data
  return {
    success: true,
    data: {
      summary: "This is a mock AI summary of the provided text.",
      keyPoints: [
        "First key point from mock AI",
        "Second key point from mock AI",
        "Third key point from mock AI"
      ],
      businessRelevance: "Mock business relevance information."
    },
    tokensUsed: 0,
    requestTime: 0,
    fromCache: false
  };
}

// Mock implementation of enhanceAboutData that matches the interface
async function enhanceAboutData(input) {
  console.log("AI Enhancement called with:", input ? JSON.stringify(input.aboutData).substring(0, 100) + '...' : 'No input');
  
  // Start with the original about data
  const enhancedData = { ...(input?.aboutData || {}) };
  
  // Add mock enhanced fields
  return {
    success: true,
    data: {
      ...enhancedData,
      summary: "Mock AI summary of the company.",
      keyInsights: ["Mock insight 1", "Mock insight 2"],
      businessModel: "Mock business model based on company description.",
      targetAudience: "Mock target audience information.",
      competitors: ["Mock competitor 1", "Mock competitor 2"],
      uniqueSellingPropositions: ["Mock USP 1", "Mock USP 2"],
      foundingYear: 2010 // Mock founding year
    },
    tokensUsed: 0,
    requestTime: 0,
    fromCache: false
  };
}

// Export for both ESM and CommonJS compatibility
export { summarizeText, enhanceAboutData };

// Add CommonJS compatibility layer
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    summarizeText,
    enhanceAboutData
  };
}
