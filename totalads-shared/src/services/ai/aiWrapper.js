/**
 * AI Service Wrapper for CommonJS and ESM compatibility
 * This file provides direct implementations using Gemini Flash
 */

import { summarizeText as summarizeTextImpl } from "./textSummarizer.js";
import { enhanceAboutData as enhanceAboutDataImpl } from "./aboutDataEnhancer.js";

// Re-export the actual implementations
async function summarizeText(input) {
	try {
		return await summarizeTextImpl(input);
	} catch (error) {
		console.error("Error in summarizeText wrapper:", error);
		// Return fallback response
		return {
			success: false,
			error: `Summarization failed: ${
				error instanceof Error ? error.message : "Unknown error"
			}`,
		};
	}
}

async function enhanceAboutData(input) {
	try {
		return await enhanceAboutDataImpl(input);
	} catch (error) {
		console.error("Error in enhanceAboutData wrapper:", error);
		// Return fallback response
		return {
			success: false,
			error: `Enhancement failed: ${
				error instanceof Error ? error.message : "Unknown error"
			}`,
		};
	}
}

// Export for both ESM and CommonJS compatibility
export { summarizeText, enhanceAboutData };

// Add CommonJS compatibility layer
if (typeof module !== "undefined" && module.exports) {
	module.exports = {
		summarizeText,
		enhanceAboutData,
	};
}
