/**
 * Type definitions for aiWrapper.js
 */
import { AboutInfo, SummarizationResult } from './types';

export interface TextSummarizerInput {
  text: string;
  websiteUrl?: string;
  additionalContext?: string;
}

export interface TextSummarizerOutput {
  summary: string;
  keyPoints: string[];
  businessRelevance: string;
}

export interface AboutDataEnhancerInput {
  aboutData: AboutInfo;
  websiteUrl?: string;
  additionalContext?: string;
}

export interface EnhancedAboutInfo extends AboutInfo {
  summary?: string;
  keyInsights?: string[];
  businessModel?: string;
  targetAudience?: string;
  competitors?: string[];
  uniqueSellingPropositions?: string[];
  foundingYear?: number;
}

/**
 * Summarizes text using AI
 * @param input Text and optional context
 * @returns Summary and key points
 */
export function summarizeText(
  input: TextSummarizerInput
): Promise<SummarizationResult<TextSummarizerOutput>>;

/**
 * Enhances about data with AI insights
 * @param input About data and optional context
 * @returns Enhanced about information
 */
export function enhanceAboutData(
  input: AboutDataEnhancerInput
): Promise<SummarizationResult<EnhancedAboutInfo>>;
