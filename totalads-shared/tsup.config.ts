import { defineConfig } from "tsup";

import loadEnv from "./src/config/loadEnv.ts";

loadEnv();

const isValidId = (str: string) => {
	try {
		new Function(`let ${str};`);
	} catch {
		return false;
	}
	return true;
};
const env: Record<string, string> = {};
Object.keys(process.env).forEach((key) => {
	const value = process.env[key];
	if (isValidId(key) && value) env[key] = value;
});

export default defineConfig({
	format: "esm",
	dts: true,
	shims: true,
	env,
	external: [
		"@google/generative-ai",
		"@aws-sdk/client-s3",
		"@aws-sdk/cloudfront-signer",
		"@aws-sdk/s3-request-presigner",
		"@node-rs/bcrypt",
		"async-retry",
		"axios",
		"bullmq",
		"cookie-parser",
		"cors",
		"dotenv",
		"drizzle-orm",
		"express",
		"http-status-codes",
		"ioredis",
		"jsonwebtoken",
		"morgan",
		"pg",
		"phone",
		"postgres",
		"resend",
		"swagger-autogen",
		"swagger-ui-express",
		"zod",
		"zod-to-json-schema",
	],
});
