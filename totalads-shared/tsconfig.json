{"compilerOptions": {"allowImportingTsExtensions": true, "noEmit": true, "module": "NodeNext", "moduleResolution": "NodeNext", "target": "ESNext", "lib": ["es6", "dom"], "rootDir": ".", "outDir": "./dist", "types": [], "typeRoots": ["./types", "./node_modules/@types"], "resolveJsonModule": true, "allowJs": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "declaration": true}, "ts-node": {"esm": true}}