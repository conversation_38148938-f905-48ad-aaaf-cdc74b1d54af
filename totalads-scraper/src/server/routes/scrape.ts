import { Router } from 'express';
import { expressAsyncHandler } from 'totalads-shared';
import { z } from 'zod';

import webPageScraper from '../../scrapers/index';

export const ScrapeURLDataSchema = z.object({
	url: z.string().url({ message: "Invalid URL" }),
});

const scrapeRouter = Router();

scrapeRouter.post(
	"/",
	expressAsyncHandler(
		async (validatedData, _, res) => {
			const result = await webPageScraper.scrape(validatedData.url);
			return res.status(200).send(result);
		},
		{
			validationSchema: ScrapeURLDataSchema,
			getValue: (req) => req.body,
		},
	),
);

export default scrapeRouter;
