import { Request, Response, Router } from "express";
import { z } from "zod";

import webPageScraper from "../../scrapers/index";

export const ScrapeURLDataSchema = z.object({
	url: z.string().url({ message: "Invalid URL" }),
});

const scrapeRouter = Router();

// Simple async handler without the complex validation wrapper
scrapeRouter.post("/", async (req: Request, res: Response) => {
	try {
		// Validate the request body
		const validationResult = ScrapeURLDataSchema.safeParse(req.body);

		if (!validationResult.success) {
			return res.status(400).send({
				error: "Invalid request data",
				details: validationResult.error.errors,
			});
		}

		const { url } = validationResult.data;
		const result = await webPageScraper.scrape(url);
		return res.status(200).send(result);
	} catch (error) {
		console.error("Error in scrape route:", error);
		return res.status(500).send({
			error: "Internal server error",
			message: error instanceof Error ? error.message : "Unknown error",
		});
	}
});

export default scrapeRouter;
