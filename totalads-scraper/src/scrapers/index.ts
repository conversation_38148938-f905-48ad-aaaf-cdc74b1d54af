import { ScrapeResult } from '../../types/scrapper';
import clusterManager from './cluster-manager';
import pageScraper from './page-scraper';

class WebPageScraper {
	async initScraper() {
		await clusterManager.initialize();
	}

	async scrape(url: string): Promise<ScrapeResult> {
		if (!clusterManager.cluster) {
			throw new Error(
				"initScraper should be called before calling scrape.",
			);
		}
		return await pageScraper.scrape(url);
	}
}

const webPageScraper = new WebPageScraper();
await webPageScraper.initScraper();

export default webPageScraper;
