import { Page } from 'puppeteer';

import { Table } from '../../types/scrapper';

export async function getTablesAndReplaceWithPlaceholders(
	page: Page,
): Promise<Table[]> {
	console.log(
		"Getting all tables in the page and replacing with placeholders.",
		page,
	);

	try {
		// Use a simpler approach to avoid the __name error
		const tables = await page.evaluate(`
			(function() {
				const result = [];

			// Helper function to clean text
			function cleanText(text) {
				return text
					.replace(/\\r\\n|\\n|\\r/gm, " ")
					.replace(/\\s+/g, " ")
					.trim();
			}

			// Process all tables - access document safely
			const doc = globalThis.document;
			if (!doc) {
				return [];
			}
			const allTables = doc.querySelectorAll("table");

			for (let i = 0; i < allTables.length; i++) {
				const table = allTables[i];

				// Skip empty tables
				if (!table?.rows || table.rows.length === 0) {
					continue;
				}

				// Skip tables with only one cell (likely layout tables)
				if (
					table.rows.length === 1 &&
					table.rows[0] &&
					table.rows[0].cells.length <= 1
				) {
					continue;
				}

				const tableData = [];
				const spanningCells = [];
				const tableHeaders = [];

				// Extract table metadata
				let tableCaption;
				const captionEl = table.querySelector("caption");
				if (captionEl && captionEl.textContent) {
					tableCaption = captionEl.textContent.trim();
				}

				const tableId = table.id || undefined;
				const tableClass = table.className || undefined;
				const tableSummary = table.getAttribute("summary") || undefined;

				// Extract headers from thead if present
				const theadRows = table.querySelectorAll("thead tr");
				if (theadRows.length > 0) {
					const headerCells = theadRows[0]?.querySelectorAll("th");
					if (headerCells) {
						for (let j = 0; j < headerCells.length; j++) {
							const headerText =
								headerCells[j]?.textContent || "";
							tableHeaders.push(cleanText(headerText));
						}
					}
				}

				// If no headers in thead, check first row for th elements
				if (tableHeaders.length === 0 && table.rows.length > 0) {
					const firstRow = table.rows[0];
					if (firstRow) {
						const firstRowHeaders = firstRow.querySelectorAll("th");
						if (firstRowHeaders.length > 0) {
							for (let j = 0; j < firstRowHeaders.length; j++) {
								const headerText =
									firstRowHeaders[j]?.textContent || "";
								tableHeaders.push(cleanText(headerText));
							}
						}
					}
				}

				// Process all rows
				const rows = table.querySelectorAll("tr");
				for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
					const row = rows[rowIndex];

					// Skip header row if we already processed it
					if (
						rowIndex === 0 &&
						tableHeaders.length > 0 &&
						row?.querySelector("th")
					) {
						continue;
					}

					const rowData = [];
					const cells = row?.querySelectorAll("td, th") || [];

					// Skip empty rows
					if (cells.length === 0) continue;

					for (
						let cellIndex = 0;
						cellIndex < cells.length;
						cellIndex++
					) {
						const cell = cells[cellIndex];

						if (cell) {
							// Handle spanning cells
							const colSpan = cell.getAttribute("colspan")
								? parseInt(
										cell.getAttribute("colspan") || "1",
										10,
									)
								: 1;
							// cspell:disable-next-line
							const rowSpan = cell.getAttribute("rowspan")
								? parseInt(
										// cspell:disable-next-line
										cell.getAttribute("rowspan") || "1",
										10,
									)
								: 1;

							if (colSpan > 1 || rowSpan > 1) {
								spanningCells.push({
									row: rowIndex,
									col: cellIndex,
									colSpan: colSpan > 1 ? colSpan : undefined,
									rowSpan: rowSpan > 1 ? rowSpan : undefined,
								});
							}

							// Extract cell content
							const cellText = cell.textContent || "";
							rowData.push(cleanText(cellText));
						}
					}

					// Only add non-empty rows
					if (rowData.some((cell) => cell.trim().length > 0)) {
						tableData.push(rowData);
					}
				}

				// Only add tables with actual data
				if (
					tableData.length > 0 &&
					tableData[0] &&
					tableData[0].length > 0
				) {
					result.push({
						tableData,
						spanningCells,
						tableCaption,
						tableHeaders:
							tableHeaders.length > 0 ? tableHeaders : undefined,
						tableId,
						tableClass,
						tableSummary,
					});

					// Replace table with placeholder
					table.innerHTML = "{table-" + i + "}";
				}
			}

				return result;
			})()
		`);

		console.log("Got all tables and replaced with placeholders:", tables);
		return tables as Table[];
	} catch (error) {
		console.error("Error extracting tables:", error);
		return []; // Return empty array on error
	}
}
