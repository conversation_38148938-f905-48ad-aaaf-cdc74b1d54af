import { Page } from 'puppeteer';

// Interfaces and Types
export interface AboutPageConfig {
	maxPages?: number;
	timeout?: number;
	includeKeywords?: string[];
	excludePatterns?: string[];
	minTextLength?: number;
}

export interface AboutPageResult {
	url: string;
	title?: string;
	matchedKeywords: string[];
	source: "dom" | "nested";
	confidence: number;
}

// Default configuration
const DEFAULT_CONFIG: Required<AboutPageConfig> = {
	maxPages: 5,
	timeout: 30000,
	includeKeywords: [
		"about",
		"about-us",
		"about us",
		"our-story",
		"our story",
		"who-we-are",
		"who we are",
		"team",
		"our-team",
		"our team",
		"meet-the-team",
		"meet the team",
		"meet-our-team",
		"meet our team",
		"leadership",
		"management",
		"company",
		"company-profile",
		"company profile",
		"mission",
		"vision",
		"values",
		"culture",
		"history",
		"staff",
		"founders",
		"executives",
		"board",
		"directors",
	],
	excludePatterns: [
		"/contact",
		"/privacy",
		"/terms",
		"/book-a-call",
		"/free-website-analysis",
		"/free-quote",
		"/get-quote",
		"/book-demo",
		"/services",
		"/cart",
		"/blog",
		"/news",
		"/reviews",
		"/testimonials",
		"/case-studies",
		"/portfolio",
		"/shop",
		"/store",
		"/faq",
		"/support",
		"/help",
		"/join-us",
		"/login",
		"/register",
		"/signin",
		"/signup",
		"/schedule-",
		"/careers",
		"/jobs",
		"/press",
		"/media-kit",
		"/download",
		"/api",
		".pdf",
		".doc",
		".zip",
		"?",
		"#",
	],
	minTextLength: 2,
};

// Helper Functions
/**
 * Validates and normalizes a URL
 */
function validateAndNormalizeUrl(url: string, baseUrl: string): string | null {
	try {
		const normalizedUrl = new URL(url, baseUrl);
		return normalizedUrl.href;
	} catch {
		return null;
	}
}

/**
 * Checks if a URL should be excluded based on patterns
 */
function shouldExcludeUrl(url: string, excludePatterns: string[]): boolean {
	const urlLower = url.toLowerCase();
	return excludePatterns.some(function(pattern) {
		return urlLower.includes(pattern.toLowerCase());
	});
}

/**
 * Calculates confidence score for a potential about page
 */
function calculateConfidence(
	url: string,
	text: string,
	keywords: string[],
): { score: number; matchedKeywords: string[] } {
	const urlLower = url.toLowerCase();
	const textLower = text.toLowerCase();
	const matchedKeywords: string[] = [];
	let score = 0;

	// Check URL path for keywords
	for (const keyword of keywords) {
		const keywordLower = keyword.toLowerCase();
		if (urlLower.includes(keywordLower)) {
			matchedKeywords.push(keyword);
			// Higher weight for URL matches
			score += keyword === "about" || keyword === "team" ? 3 : 2;
		}
	}

	// Check link text for keywords
	for (const keyword of keywords) {
		const keywordLower = keyword.toLowerCase();
		if (textLower.includes(keywordLower)) {
			if (!matchedKeywords.includes(keyword)) {
				matchedKeywords.push(keyword);
			}
			// Lower weight for text matches
			score += keyword === "about" || keyword === "team" ? 2 : 1;
		}
	}

	return { score, matchedKeywords };
}

/**
 * Finds about and team pages from DOM links
 */
async function findAboutPagesFromDOM(
	page: Page,
	baseUrl: string,
	config: Required<AboutPageConfig>,
): Promise<AboutPageResult[]> {
	try {
		const results = await page.evaluate(
			function(baseUrl, keywords, excludePatterns, maxPages, minTextLength) {
				const results = []; 
				const seenUrls = new Set(); 

				// Helper function to calculate confidence (duplicated for browser context)
				function calculateConfidenceInBrowser(url: string, text: string, keywords: string[]) {
					const urlLower = url.toLowerCase();
					const textLower = text.toLowerCase();
					const matchedKeywords = []; // Plain JavaScript array, no TypeScript typing
					let score = 0;

					for (const keyword of keywords) {
						const keywordLower = keyword.toLowerCase();
						if (urlLower.includes(keywordLower)) {
							matchedKeywords.push(keyword);
							score +=
								keyword === "about" || keyword === "team"
									? 3
									: 2;
						}
						if (textLower.includes(keywordLower)) {
							if (!matchedKeywords.includes(keyword)) {
								matchedKeywords.push(keyword);
							}
							score +=
								keyword === "about" || keyword === "team"
									? 2
									: 1;
						}
					}

					return { score, matchedKeywords };
				}

				const links = document.querySelectorAll("a[href]");
				const baseUrlObj = new URL(baseUrl);

				for (const link of Array.from(links)) {
					if (results.length >= maxPages) break;

					const href = (link as HTMLAnchorElement).href;
					const text = (link.textContent || "").trim();

					// Skip invalid or irrelevant links
					if (
						!href ||
						text.length < minTextLength ||
						href.startsWith("javascript:") ||
						href.startsWith("#") ||
						href.startsWith("tel:") ||
						href.startsWith("mailto:")
					) {
						continue;
					}

					try {
						const url = new URL(href);

						// Only process same-domain links
						if (url.hostname !== baseUrlObj.hostname) continue;

						// Check exclusion patterns
						const urlLower = href.toLowerCase();
						if (
							excludePatterns.some(function(pattern) {
								return urlLower.includes(pattern.toLowerCase());
							})
						) {
							continue;
						}

						// Calculate confidence
						const { score, matchedKeywords } =
							calculateConfidenceInBrowser(href, text, keywords);

						if (score > 0 && matchedKeywords.length > 0) {
							const normalizedUrl =
								url.origin + url.pathname.replace(/\/$/, "");

							if (!seenUrls.has(normalizedUrl)) {
								seenUrls.add(normalizedUrl);
								results.push({
									url: href,
									title: text,
									matchedKeywords,
									confidence: score,
								});
							}
						}
					} catch {
						// Skip invalid URLs
						continue;
					}
				}

				// Sort by confidence score (highest first)
				return results.sort(function(a, b) { return b.confidence - a.confidence; });
			},
			baseUrl,
			config.includeKeywords,
			config.excludePatterns,
			config.maxPages,
			config.minTextLength,
		);

		return results.map(function(result) {
			return {
				...result,
				source: "dom" as const,
			};
		});
	} catch (error) {
		console.error("Error finding about pages from DOM:", error);
		return [];
	}
}

/**
 * Processes nested links to find about pages
 */
function findAboutPagesFromNested(
	nestedLinks: string[],
	baseUrl: string,
	config: Required<AboutPageConfig>,
): AboutPageResult[] {
	const results: AboutPageResult[] = [];
	const seenUrls = new Set<string>();
	const baseUrlObj = new URL(baseUrl);

	for (const link of nestedLinks) {
		if (results.length >= config.maxPages) break;

		const normalizedUrl = validateAndNormalizeUrl(link, baseUrl);
		if (!normalizedUrl) continue;

		try {
			const url = new URL(normalizedUrl);

			// Only process same-domain links
			if (url.hostname !== baseUrlObj.hostname) continue;

			// Check exclusion patterns
			if (shouldExcludeUrl(normalizedUrl, config.excludePatterns))
				continue;

			// Calculate confidence
			const { score, matchedKeywords } = calculateConfidence(
				normalizedUrl,
				"",
				config.includeKeywords,
			);

			if (score > 0 && matchedKeywords.length > 0) {
				const finalUrl = url.origin + url.pathname.replace(/\/$/, "");

				if (!seenUrls.has(finalUrl)) {
					seenUrls.add(finalUrl);
					results.push({
						url: normalizedUrl,
						matchedKeywords,
						confidence: score,
						source: "nested",
					});
				}
			}
		} catch (error) {
			console.error("Error processing nested link:", link, error);
		}
	}

	return results.sort(function(a, b) { return b.confidence - a.confidence; });
}


/**
 * Finds about and team pages on a website with improved robustness
 * @param page - Puppeteer page instance
 * @param baseUrl - Base URL of the website
 * @param nestedLinks - Optional array of links to check
 * @param config - Optional configuration object
 * @returns Promise containing array of relevant page results
 */
export async function findAboutAndTeamPages(
	page: Page,
	baseUrl: string,
	nestedLinks?: string[],
	config: AboutPageConfig = {},
): Promise<AboutPageResult[]> {
	const mergedConfig = { ...DEFAULT_CONFIG, ...config };

	console.log(`🔍 Finding about and team pages from: ${baseUrl}`);
	console.log(
		`📋 Config: maxPages=${mergedConfig.maxPages}, timeout=${mergedConfig.timeout}ms`,
	);

	try {
		// Validate base URL
		const baseUrlObj = new URL(baseUrl);
		console.log(`✅ Base URL validated: ${baseUrlObj.hostname}`);

		// Set page timeout
		page.setDefaultTimeout(mergedConfig.timeout);

		// First, try to find pages from DOM
		const domResults = await findAboutPagesFromDOM(
			page,
			baseUrl,
			mergedConfig,
		);

		if (domResults.length > 0) {
			console.log(
				`✅ Found ${domResults.length} about/team pages from DOM`,
			);
			domResults.forEach(function(result) {
				console.log(
					`  📄 ${result.url} (confidence: ${result.confidence}, keywords: ${result.matchedKeywords.join(", ")})`,
				);
			});
			return domResults.slice(0, mergedConfig.maxPages);
		}

		// Fallback to nested links if no DOM results
		if (nestedLinks && nestedLinks.length > 0) {
			console.log(
				`🔄 No DOM results found, processing ${nestedLinks.length} nested links...`,
			);

			const nestedResults = findAboutPagesFromNested(
				nestedLinks,
				baseUrl,
				mergedConfig,
			);

			if (nestedResults.length > 0) {
				console.log(
					`✅ Found ${nestedResults.length} about/team pages from nested links`,
				);
				nestedResults.forEach(function(result) {
					console.log(
						`  📄 ${result.url} (confidence: ${result.confidence}, keywords: ${result.matchedKeywords.join(", ")})`,
					);
				});
				return nestedResults.slice(0, mergedConfig.maxPages);
			}
		}

		console.log("❌ No about/team pages found");
		return [];
	} catch (error) {
		console.error("💥 Error finding about and team pages:", error);

		// Enhanced error handling with specific error types
		if (error instanceof Error) {
			if (error.message.includes("timeout")) {
				console.error(
					"⏰ Timeout error - consider increasing timeout value",
				);
			} else if (error.message.includes("net::")) {
				console.error(
					"🌐 Network error - check internet connection and URL accessibility",
				);
			} else if (error.message.includes("Navigation")) {
				console.error(
					"🧭 Navigation error - page may not be accessible",
				);
			}
		}

		return [];
	}
}
