/* eslint-disable no-undef */
import { Page } from "puppeteer";
// Using direct TypeScript wrapper to avoid ESM/CommonJS compatibility issues
import { summarizeText } from "totalads-shared/src/services/ai/aiWrapper";

// Type definitions for the enhanced result
interface BasicInfo {
	title: string | null;
	desc: string | null;
	summary?: string;
	keyPoints?: string[];
	businessInfo?: string;
}

export async function getTitleAndDesc(page: Page): Promise<BasicInfo> {
	console.log("Getting title and desc of the page");

	// Get basic page metadata
	const result = await page.evaluate(() => {
		let title = document.head.querySelector("title")?.innerHTML || null,
			desc = null;
		document.head.querySelectorAll("meta").forEach((meta) => {
			const metaPropertyName =
				meta.getAttribute("name") ||
				meta.getAttribute("property") ||
				"";
			if (["title", "og:title"].includes(metaPropertyName))
				title = meta.getAttribute("content");
			else if (
				["description", "og:description"].includes(metaPropertyName)
			)
				desc = meta.getAttribute("content");
		});

		// Also capture some additional text from the page for better context
		let bodyText: string = "";

		// Try to get main content
		const mainContent =
			document.querySelector("main") ||
			document.querySelector("article") ||
			document.querySelector("#content") ||
			document.querySelector(".content") ||
			null;

		if (mainContent && mainContent.textContent) {
			bodyText = mainContent.textContent;
		} else {
			// Fall back to first few paragraphs if no main content identified
			const paragraphs = document.querySelectorAll("p");
			let textArray = [];

			// Get text from first 5 substantial paragraphs
			for (
				let i = 0;
				i < paragraphs.length && textArray.length < 5;
				i++
			) {
				const paragraph = paragraphs[i];
				if (paragraph) {
					const text = paragraph.textContent?.trim() || "";
					if (text.length > 50) {
						textArray.push(text);
					}
				}
			}

			bodyText = textArray.join(" ");
		}

		return {
			title,
			desc,
			bodyText: bodyText.substring(0, 5000), // Limit to 5000 chars to avoid huge payloads
		};
	});

	console.log(`Title - ${result?.title || "N/A"}
Desc - ${result?.desc || "N/A"}`);

	// Create enhanced summary using the AI if we have enough text
	// Create a new object with only the properties we want to return
	const enhancedResult: BasicInfo = {
		title: result?.title || null,
		desc: result?.desc || null,
	};

	if (
		result &&
		(result.title ||
			result.desc ||
			(result.bodyText && result.bodyText.length > 100))
	) {
		try {
			// Combine all available text for summarization
			const textToSummarize = [
				`Title: ${result?.title || "N/A"}`,
				`Description: ${result?.desc || "N/A"}`,
				`Content: ${result?.bodyText || ""}`,
			].join("\n\n");

			const summaryResult = await summarizeText({
				text: textToSummarize,
			});

			if (summaryResult?.success && summaryResult?.data) {
				// Add the summary and key points to the result
				enhancedResult.summary = summaryResult.data.summary;
				enhancedResult.keyPoints = summaryResult.data.keyPoints;
				enhancedResult.businessInfo =
					summaryResult.data.businessRelevance;

				console.log(
					"Generated AI summary for basic info using Gemini Flash",
				);
			} else if (summaryResult?.error) {
				console.warn("AI summarization failed:", summaryResult.error);
			}
		} catch (error) {
			console.error("Failed to generate AI summary:", error);
		}
	}

	return enhancedResult;
}

export async function getNestedLinks(
	page: Page,
	normalizeURLFn: (url: string) => string,
) {
	console.log("Getting all nested links in the page");
	const nestedAnchorsHrefs = await page.evaluate(() => {
		const nestedAnchors = document.querySelectorAll("a");
		const nestedAnchorsHrefs: string[] = [];
		nestedAnchors.forEach((nestedAnchor) =>
			nestedAnchorsHrefs.push(nestedAnchor.href),
		);
		return nestedAnchorsHrefs;
	});

	const currentPageURL = new URL(normalizeURLFn(page.url()));
	const nestedLinks = new Set<string>();

	nestedAnchorsHrefs.forEach((href) => {
		let nestedAnchorHref = href;
		let nestedURL: URL;
		try {
			nestedURL = new URL(normalizeURLFn(href));
		} catch {
			nestedAnchorHref = `${currentPageURL.origin}${
				nestedAnchorHref.startsWith("/") ? "" : "/"
			}${nestedAnchorHref}`;
			try {
				nestedURL = new URL(normalizeURLFn(nestedAnchorHref));
			} catch {
				return;
			}
		}
		if (
			nestedURL.origin === currentPageURL.origin &&
			nestedURL.href !== currentPageURL.href
		)
			nestedLinks.add(nestedURL.href);
	});

	console.log(`Nested links: ${Array.from(nestedLinks)}`);
	return nestedLinks;
}

export async function removeUnnecessaryElements(
	page: Page,
	elementsToRemove: string[],
) {
	console.log("Removing unnecessary elements for the page");
	const removedCount = await page.evaluate((elementsToRemove) => {
		let removedCount = 0;
		document.querySelectorAll(elementsToRemove.join(", ")).forEach((el) => {
			removedCount++;
			el.remove();
		});
		return removedCount;
	}, elementsToRemove);
	console.log(`Removed ${removedCount} unnecessary elements from the page`);
}
