/* eslint-disable no-undef */
import { Page } from 'puppeteer';

import { ContactDetails } from '../../types/scrapper';

export async function extractContactDetailsFromPage(
	page: Page,
): Promise<ContactDetails> {
	return await page.evaluate(() => {
		const contactDetails: ContactDetails = {
			email: [],
			phone: [],
			socialLinks: {},
		};

		// Extract company/organization name
		const nameSelectors = [
			"h1", // Often the main heading is the company name
			".company-name",
			".org-name",
			".site-title",
			'meta[property="og:site_name"]',
			"#logo img",
			".logo img",
		];

		for (const selector of nameSelectors) {
			let nameElement;
			try {
				if (selector === 'meta[property="og:site_name"]') {
					nameElement = document.querySelector(selector);
					if (nameElement) {
						contactDetails.name =
							nameElement.getAttribute("content") ?? undefined;
						break;
					}
				} else if (selector.includes("img")) {
					nameElement = document.querySelector(selector);
					if (nameElement) {
						contactDetails.name = nameElement
							.getAttribute("alt")
							?.replace(" logo", "");
						break;
					}
				} else {
					nameElement = document.querySelector(selector);
					if (nameElement && nameElement.textContent?.trim()) {
						contactDetails.name = nameElement.textContent.trim();
						break;
					}
				}
			} catch (e) {
				console.log("Error extracting name:", e);
				// Continue if selector fails
			}
		}

		// Extract emails using regex
		const emailRegex =
			/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
		const pageText = document.body.innerText;
		const emailMatches = pageText.match(emailRegex) || [];

		contactDetails.email = Array.from(new Set(emailMatches));

		// Extract from mailto links
		document.querySelectorAll('a[href^="mailto:"]').forEach((el) => {
			const mailtoHref = el.getAttribute("href");
			if (mailtoHref) {
				const email = mailtoHref
					?.replace("mailto:", "")
					?.split("?")[0]
					?.trim();
				if (email && !contactDetails.email!.includes(email)) {
					contactDetails.email!.push(email);
				}
			}
		});

		// Extract phone numbers
		const phoneRegex =
			/(?:\+\d{1,3}[- ]?)?\(?\d{3}\)?[- ]?\d{3}[- ]?\d{4}/g;
		const phoneMatches = pageText.match(phoneRegex) || [];
		contactDetails.phone = Array.from(new Set(phoneMatches));

		// Additional extraction from tel: links
		document.querySelectorAll('a[href^="tel:"]').forEach((el) => {
			const telHref = el.getAttribute("href");
			if (telHref) {
				const phone = telHref.replace("tel:", "").trim();
				if (phone && !contactDetails.phone!.includes(phone)) {
					contactDetails.phone!.push(phone);
				}
			}
		});

		// Extract address
		// Look for address in structured data
		const addressSelectors = [
			".address",
			".contact-address",
			"address",
			".location",
			'[itemprop="address"]',
			".vcard",
			".contact-info address",
			".footer address",
			".contact address",
		];

		for (const selector of addressSelectors) {
			try {
				const addressElement = document.querySelector(selector);
				if (addressElement && addressElement.textContent?.trim()) {
					contactDetails.address = addressElement.textContent
						.trim()
						.replace(/\s+/g, " ") // Replace multiple spaces with single space
						.replace(/\n+/g, ", "); // Replace newlines with commas
					break;
				}
			} catch (e) {
				console.log("Error extracting address:", e);
				// Continue if selector fails
			}
		}

		// If address not found, try to find address-like content
		if (!contactDetails.address) {
			// Try to find address by looking for patterns in text containing postal codes or states
			const addressRegex =
				/\b(\d{1,5}\s[\w\s-]{1,20}(?:street|st|avenue|ave|road|rd|highway|hwy|square|sq|trail|trl|drive|dr|court|ct|park|parkway|pkwy|circle|cir|boulevard|blvd|way|lane|ln)\s?(?:[,]\s?)?(?:[a-zA-Z]{2,30}\s)?(?:[\d]{5})?)(?=[,\n]|$)/i;

			// Find all paragraphs that may contain address info
			document.querySelectorAll("p").forEach((para) => {
				if (contactDetails.address) return;

				const text = para.textContent || "";
				const match = text.match(addressRegex);
				if (match) {
					contactDetails.address = match[0].trim();
				}
			});
		}

		// Extract social media links
		const socialPlatforms = {
			"facebook.com": "facebook",
			"twitter.com": "twitter",
			"x.com": "twitter",
			"linkedin.com": "linkedin",
			"instagram.com": "instagram",
			"youtube.com": "youtube",
			"pinterest.com": "pinterest",
			"tiktok.com": "tiktok",
		};

		document.querySelectorAll("a[href]").forEach((link) => {
			const href = link.getAttribute("href");
			if (!href) return;

			for (const [domain, platform] of Object.entries(socialPlatforms)) {
				if (href.includes(domain)) {
					contactDetails.socialLinks![platform] = href;
					break;
				}
			}
		});

		return contactDetails;
	});
}

export async function findContactPages(
	page: Page,
	baseUrl: string,
): Promise<string[]> {
	console.log("Finding contact/about pages");

	return await page.evaluate((baseUrl) => {
		const contactPageKeywords = [
			"contact",
			"about",
			"about-us",
			"about us",
			"get in touch",
			"reach us",
			"connect",
			"location",
			"find us",
			"meet",
			"team",
			"support",
			"help",
			"contact-us",
			"contact us",
		];
		const contactLinks: string[] = [];

		// Find all links
		document.querySelectorAll("a").forEach((link) => {
			const href = link.getAttribute("href");
			const text = link.textContent?.toLowerCase().trim() || "";

			if (!href) return;

			// Check if link text or href contains contact-related keywords
			const isContactLink = contactPageKeywords.some(
				(keyword) =>
					text.includes(keyword) ||
					href.toLowerCase().includes(keyword),
			);

			if (isContactLink) {
				let fullUrl = href;

				// Handle relative URLs
				if (href.startsWith("/")) {
					try {
						const url = new URL(baseUrl);
						fullUrl = `${url.origin}${href}`;
					} catch (e) {
						console.log(`Error parsing URL: ${baseUrl}`, e);
						return;
					}
				} else if (!href.startsWith("http")) {
					fullUrl = `${baseUrl}${href.startsWith("/") ? "" : "/"}${href}`;
				}

				// Only add URLs from the same domain
				try {
					const linkUrl = new URL(fullUrl);
					const baseUrlObj = new URL(baseUrl);

					if (linkUrl.hostname === baseUrlObj.hostname) {
						contactLinks.push(fullUrl);
					}
				} catch (e) {
					console.log(`Error parsing URL: ${fullUrl}`, e);
					// Skip invalid URLs
				}
			}
		});

		return contactLinks;
	}, baseUrl);
}

export function isContactDetailsComplete(details: ContactDetails): boolean {
	// Consider details complete if we have at least one email and phone number
	const hasEmail = details.email && details.email.length > 0;
	const hasPhone = details.phone && details.phone.length > 0;

	// Return true if we have both email and phone
	return Boolean(hasEmail && hasPhone);
}

export function mergeContactDetails(
	existing: ContactDetails,
	additional: ContactDetails,
): ContactDetails {
	const merged: ContactDetails = { ...existing };

	// Merge emails
	if (additional.email && additional.email.length > 0) {
		merged.email = Array.from(
			new Set([...(existing.email || []), ...additional.email]),
		);
	}

	// Merge phones
	if (additional.phone && additional.phone.length > 0) {
		merged.phone = Array.from(
			new Set([...(existing.phone || []), ...additional.phone]),
		);
	}

	// Set address if it doesn't exist
	if (!existing.address && additional.address) {
		merged.address = additional.address;
	}

	// Set name if it doesn't exist
	if (!existing.name && additional.name) {
		merged.name = additional.name;
	}

	// Merge social links
	merged.socialLinks = {
		...(existing.socialLinks || {}),
		...(additional.socialLinks || {}),
	};

	return merged;
}
