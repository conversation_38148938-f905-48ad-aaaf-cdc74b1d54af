import { Page } from 'puppeteer';

import { CompanyInfo } from '../../types/scrapper';
import { extractAboutInfo } from './about-details-extractor';
import { AboutPageConfig, AboutPageResult, findAboutAndTeamPages } from './page-finder';
import { extractTeamInfo, isTeamInfoComplete, mergeTeamInfo } from './team-info';


/**
 * Extracts both about and team information from a page
 * @param page - Puppeteer page instance
 * @returns Promise containing company info
 */
export async function extractAboutAndTeamInfo(
	page: Page,
	// basePayload: any,
	// nestedLinks?: string[],
): Promise<CompanyInfo> {
	console.log("Extracting about and team information from current page only");

	try {
		// Get the base URL
		const baseUrl = page.url();

		// Extract info from the current page only - no navigation
		const aboutInfo = await extractAboutInfo(page, baseUrl);

		// Get team information
		const teamMembers = await extractTeamInfo(page);

		// Return combined info - just merge the about info with team members
		return {
			...aboutInfo,
			teamMembers,
		};
	} catch (error) {
		console.error("Error in extractAboutAndTeamInfo:", error);
		return {
			companyDescription: undefined,
			foundingInfo: undefined,
			missionStatement: undefined,
			teamMembers: undefined,
			companyValues: undefined,
			awards: undefined,
			industries: undefined,
			globalPresence: undefined,
			officeLocations: undefined,
			certifications: undefined,
		};
	}
}

/**
 * Checks if about details are complete enough to be considered done
 * @param info - Company information object
 * @returns boolean indicating if information is complete
 */
export function isAboutDetailsComplete(info: CompanyInfo): boolean {
	// Check if we have the essential about page information
	// We consider it complete if we have at least 3 out of these 5 key pieces of information
	let completenessScore = 0;

	if (info.companyDescription && info.companyDescription.length > 100) {
		completenessScore += 1;
	}

	if (info.foundingInfo && info.foundingInfo.length > 10) {
		completenessScore += 1;
	}

	if (info.missionStatement && info.missionStatement.length > 30) {
		completenessScore += 1;
	}

	if (info.companyValues && info.companyValues.length >= 2) {
		completenessScore += 1;
	}

	if (info.industries && info.industries.length >= 1) {
		completenessScore += 1;
	}

	// Check team info completeness if it exists
	const hasTeamInfo = info.teamMembers && info.teamMembers.length > 0;
	const teamInfoComplete = hasTeamInfo
		? isTeamInfoComplete(info.teamMembers)
		: false;

	// Either very complete about info OR decent about info + team info
	return (
		completenessScore >= 3 || (completenessScore >= 2 && teamInfoComplete)
	);
}

/**
 * Merges two company information objects into one.
 *
 * @param existing - Existing company information
 * @param additional - Additional company information to merge
 * @returns Merged company information
 */
export function mergeCompanyInfo(
	existing: CompanyInfo,
	additional: CompanyInfo,
): CompanyInfo {
	const merged: CompanyInfo = { ...existing };

	// Use additional company description if existing one is missing or shorter
	if (additional.companyDescription) {
		if (
			!existing.companyDescription ||
			additional.companyDescription.length >
				existing.companyDescription.length
		) {
			merged.companyDescription = additional.companyDescription;
		}
	}

	// Use additional founding info if existing one is missing
	if (!existing.foundingInfo && additional.foundingInfo) {
		merged.foundingInfo = additional.foundingInfo;
	}

	// Use additional mission statement if existing one is missing
	if (!existing.missionStatement && additional.missionStatement) {
		merged.missionStatement = additional.missionStatement;
	}

	// Merge team members using the specialized team merger function
	merged.teamMembers = mergeTeamInfo(
		existing.teamMembers,
		additional.teamMembers,
	);

	// Merge company values
	if (additional.companyValues && additional.companyValues.length > 0) {
		merged.companyValues = Array.from(
			new Set([
				...(existing.companyValues || []),
				...additional.companyValues,
			]),
		);
	}

	// Merge awards
	if (additional.awards && additional.awards.length > 0) {
		merged.awards = Array.from(
			new Set([...(existing.awards || []), ...additional.awards]),
		);
	}

	// Merge industries
	if (additional.industries && additional.industries.length > 0) {
		merged.industries = Array.from(
			new Set([...(existing.industries || []), ...additional.industries]),
		);
	}

	return merged;
}

/**
 * Main orchestrator to extract comprehensive company information from a website.
 * It finds relevant pages (about, team) and then extracts details from each.
 * @param page - Puppeteer page instance
 * @param initialUrl - The initial URL of the website to scrape
 * @param config - Optional configuration for page finding
 * @returns Promise containing aggregated company information
 */
export async function extractCompanyInfoFromSite(
	page: Page,
	initialUrl: string,
	config?: AboutPageConfig,
): Promise<CompanyInfo> {
	let masterCompanyInfo: CompanyInfo = {
		teamMembers: [], // Initialize with empty array for mergeTeamInfo
	};
	const visitedUrls = new Set<string>();

	try {
		console.log(`Starting company info extraction for site: ${initialUrl}`);

		// 1. Find potential about and team pages
		const allFoundPages: AboutPageResult[] = await findAboutAndTeamPages(
			page,
			initialUrl,
			undefined,
			config,
		); // Corrected call signature

		const aboutPageUrls = new Set<string>();
		const teamPageUrls = new Set<string>();

		allFoundPages.forEach((foundPage: AboutPageResult) => {
			// Heuristic to differentiate pages based on matched keywords.
			// A page could potentially be categorized as both.
			const lcKeywords = foundPage.matchedKeywords.map((k) =>
				k.toLowerCase(),
			);
			let isAboutPage = false;
			let isTeamPage = false;

			const aboutKeywords = [
				"about",
				"company",
				"mission",
				"vision",
				"history",
				"story",
			];
			if (
				lcKeywords.some((keyword) =>
					aboutKeywords.some((ak) => keyword.includes(ak)),
				)
			) {
				isAboutPage = true;
			}

			const teamKeywords = [
				"team",
				"leadership",
				"management",
				"founders",
				"executives",
				"board",
				"staff",
				"careers",
			]; // Added 'careers' as it often leads to team info
			if (
				lcKeywords.some((keyword) =>
					teamKeywords.some((tk) => keyword.includes(tk)),
				)
			) {
				isTeamPage = true;
			}

			if (isAboutPage) {
				aboutPageUrls.add(foundPage.url);
			}
			if (isTeamPage) {
				teamPageUrls.add(foundPage.url);
			}
			// If a page didn't strongly match either but was returned, consider adding its URL
			// to a general pool or prioritizing based on confidence score if available and relevant.
			// For now, adding to specific sets if keywords match.
		});
		console.log(
			`Found pages: About URLs - ${aboutPageUrls.size}, Team URLs - ${teamPageUrls.size}`,
		);

		// 2. Create a list of URLs to process, ensuring uniqueness and starting with initialUrl
		const urlsToProcess = new Set<string>();
		urlsToProcess.add(initialUrl); // Start with the initial page
		aboutPageUrls.forEach((url) => urlsToProcess.add(url));
		teamPageUrls.forEach((url) => urlsToProcess.add(url));

		// 3. Process each unique URL
		for (const url of urlsToProcess) {
			if (visitedUrls.has(url)) {
				console.log(`Skipping already visited URL: ${url}`);
				continue;
			}

			try {
				console.log(`Navigating to page: ${url}`);
				if (page.url() !== url) {
					await page.goto(url, {
						waitUntil: "networkidle2",
						timeout: 60000,
					});
				}
				visitedUrls.add(url);

				// Extract about info from the current page
				const aboutInfo = await extractAboutInfo(page, initialUrl); // Pass initialUrl as baseUrl context

				// Extract team info from the current page
				const teamMembers = await extractTeamInfo(page);

				const currentPageInfo: CompanyInfo = {
					...aboutInfo,
					teamMembers,
				};

				// Merge with master info
				masterCompanyInfo = mergeCompanyInfo(
					masterCompanyInfo,
					currentPageInfo,
				);

				// Check for completeness
				const aboutComplete = isAboutDetailsComplete(masterCompanyInfo);
				const teamComplete = masterCompanyInfo.teamMembers
					? isTeamInfoComplete(masterCompanyInfo.teamMembers)
					: false;

				if (aboutComplete && teamComplete) {
					console.log(
						"Sufficient information gathered. Stopping further page processing.",
					);
					break; // Exit loop if all desired info is gathered
				}
			} catch (pageError) {
				console.error(`Error processing page ${url}:`, pageError);
				// Continue to the next page if one fails
			}
		}

		console.log("Finished company info extraction from site.");
		return masterCompanyInfo;
	} catch (error) {
		console.error("Critical error in extractCompanyInfoFromSite:", error);
		// Return what has been gathered so far, or a minimal object
		return masterCompanyInfo.companyDescription ||
			(masterCompanyInfo.teamMembers &&
				masterCompanyInfo.teamMembers.length > 0)
			? masterCompanyInfo
			: {
					companyDescription: undefined,
					foundingInfo: undefined,
					missionStatement: undefined,
					teamMembers: undefined,
					companyValues: undefined,
					awards: undefined,
					industries: undefined,
					globalPresence: undefined,
					officeLocations: undefined,
					certifications: undefined,
				};
	}
}
