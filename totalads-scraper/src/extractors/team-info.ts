/* eslint-disable no-undef */
import { Page } from "puppeteer";

/**
 * Interface representing a team member with their details
 */
export interface TeamMember {
  name: string;
  title?: string;
  bio?: string;
  imageUrl?: string;
  contact?: {
    email?: string;
    phone?: string;
    linkedin?: string;
  };
}

/**
 * Extracts team information from a webpage
 * @param page - Puppeteer page instance
 * @returns Promise containing an array of team members
 */
export async function extractTeamInfo(page: Page): Promise<TeamMember[]> {
  console.log("Extracting team information");

  try {
    // Use string-based evaluation to completely avoid TypeScript transpilation issues
    const result = await page.evaluate(`
      (function() {
        // Array to store team members
        const teamMembers = [];
        
        // Function to check if an element matches a regex pattern
        function matchesRegex(element, pattern, attribute = 'class') {
          const attributeValue = element.getAttribute(attribute);
          return attributeValue ? new RegExp(pattern).test(attributeValue) : false;
        }

        // Function to find elements by text content
        function getElementsByText(tag, text) {
          const elements = document.querySelectorAll(tag);
          return Array.from(elements).filter(function(element) {
            return element.textContent && element.textContent.toLowerCase().includes(text.toLowerCase());
          });
        }
      
        // 1. Look for team sections by class name patterns
        const teamSectionPatterns = [
          /team/i, /staff/i, /people/i, /employee/i, /member/i, /leadership/i, /executive/i,
          /management/i, /director/i, /founder/i, /board/i, /about.*team/i, /our.*team/i
        ];
        
        // Find potential team sections by class regex
        const potentialTeamSections = [];
        document.querySelectorAll('section, div, ul').forEach(function(element) {
          for (const pattern of teamSectionPatterns) {
            if (matchesRegex(element, pattern) || 
                matchesRegex(element, pattern, 'id')) {
              potentialTeamSections.push(element);
              break;
            }
          }
        });
        
        // 2. Look for team section by heading text
        const teamHeadings = getElementsByText('h1, h2, h3, h4, h5', 'team');
        teamHeadings.push.apply(teamHeadings, getElementsByText('h1, h2, h3, h4, h5', 'leadership'));
        teamHeadings.push.apply(teamHeadings, getElementsByText('h1, h2, h3, h4, h5', 'people'));
        teamHeadings.push.apply(teamHeadings, getElementsByText('h1, h2, h3, h4, h5', 'staff'));
        teamHeadings.push.apply(teamHeadings, getElementsByText('h1, h2, h3, h4, h5', 'management'));
        teamHeadings.push.apply(teamHeadings, getElementsByText('h1, h2, h3, h4, h5', 'executives'));
        teamHeadings.push.apply(teamHeadings, getElementsByText('h1, h2, h3, h4, h5', 'directors'));
        teamHeadings.push.apply(teamHeadings, getElementsByText('h1, h2, h3, h4, h5', 'founders'));

        // Get parent or next sibling of headings as potential team sections
        teamHeadings.forEach(function(heading) {
          // Try next sibling first
          if (heading.nextElementSibling) {
            potentialTeamSections.push(heading.nextElementSibling);
          }
          // If that doesn't work, try parent's parent (grandparent)
          else if (heading.parentElement && heading.parentElement.parentElement) {
            potentialTeamSections.push(heading.parentElement.parentElement);
          }
        });
        
        // 3. Look through traditional selectors as a fallback
        const teamSelectors = [
          ".team-member", ".team-card", ".member", ".employee", ".staff-member",
          ".people-card", ".bio-card", ".team-grid > div", ".leadership-team > div",
          ".people > div", ".team > div", ".our-team > div", ".executives > div",
          ".management > div", ".team-section", ".about-team", ".team-list",
          ".team-info", ".team-block", ".team-members"
        ];

        // Add elements from traditional selectors to potential sections
        for (const selector of teamSelectors) {
          try {
            const elements = document.querySelectorAll(selector);
            if (elements && elements.length > 0) {
              for (let i = 0; i < elements.length; i++) {
                potentialTeamSections.push(elements[i]);
              }
            }
          } catch (e) {
            // Ignore invalid selectors
          }
        }
        
        // Helper function to extract team member information
        function extractTeamMemberInfo(member) {
          const teamMember = {
            name: '',
            title: undefined,
            bio: undefined,
            imageUrl: undefined,
            contact: {
              email: undefined,
              phone: undefined,
              linkedin: undefined
            }
          };
          
          // Extract name - look for headings first
          const nameHeading = member.querySelector('h1, h2, h3, h4, h5, h6');
          if (nameHeading && nameHeading.textContent) {
            teamMember.name = nameHeading.textContent.trim();
          }
          
          // Fallback for name - look for elements with name-related classes
          if (!teamMember.name) {
            const nameSelectors = [
              '.name', '.member-name', '.team-name', '.employee-name',
              '[class*="name"]', '[itemprop="name"]'
            ];
            
            for (const selector of nameSelectors) {
              try {
                const nameElement = member.querySelector(selector);
                if (nameElement && nameElement.textContent) {
                  teamMember.name = nameElement.textContent.trim();
                  break;
                }
              } catch (e) {
                // Ignore invalid selectors
              }
            }
          }
          
          // Extract title/position
          const titleSelectors = [
            '.title', '.position', '.role', '.job-title', '.designation',
            '[class*="title"]', '[class*="position"]', '[class*="role"]',
            '[itemprop="jobTitle"]'
          ];
          
          for (const selector of titleSelectors) {
            try {
              const titleElement = member.querySelector(selector);
              if (titleElement && titleElement.textContent) {
                teamMember.title = titleElement.textContent.trim();
                break;
              }
            } catch (e) {
              // Ignore invalid selectors
            }
          }
          
          // Extract bio
          const bioSelectors = [
            '.bio', '.description', '.about', '.member-bio', '.member-description',
            '.team-bio', '.team-description', '.profile', '.summary',
            '[class*="bio"]', '[class*="description"]', '[itemprop="description"]'
          ];
          
          for (const selector of bioSelectors) {
            try {
              const bioElement = member.querySelector(selector);
              if (bioElement && bioElement.textContent) {
                teamMember.bio = bioElement.textContent.trim();
                break;
              }
            } catch (e) {
              // Ignore invalid selectors
            }
          }
          
          // Extract image URL
          const imageElement = member.querySelector('img, [class*="image"], [class*="photo"], [class*="picture"]');
          if (imageElement) {
            // For regular images
            if (imageElement.tagName === 'IMG') {
              teamMember.imageUrl = imageElement.src;
            }
            // For background images
            else {
              const bgImageStyle = window.getComputedStyle(imageElement).backgroundImage;
              if (bgImageStyle && bgImageStyle !== 'none') {
                const match = bgImageStyle.match(/url\(["']?([^"']*)[""]?\)/);
                if (match && match[1]) {
                  teamMember.imageUrl = match[1];
                }
              }
            }
          }
          
          // Extract contact information
          // Email
          const emailLink = member.querySelector('a[href^="mailto:"]');
          if (emailLink && emailLink.href) {
            const email = emailLink.href.replace('mailto:', '').trim();
            teamMember.contact.email = email;
          }
          
          // Phone
          const phoneLink = member.querySelector('a[href^="tel:"]');
          if (phoneLink && phoneLink.href) {
            const phone = phoneLink.href.replace('tel:', '').trim();
            teamMember.contact.phone = phone;
          }
          
          // LinkedIn
          const linkedinLink = member.querySelector(
            'a[href*="linkedin.com"], [class*="linkedin"], [class*="social"][href*="linkedin"]'
          );
          if (linkedinLink && linkedinLink.href) {
            teamMember.contact.linkedin = linkedinLink.href;
          }
          
          return teamMember;
        }
        
        // Process potential team sections to extract member information
        for (let i = 0; i < potentialTeamSections.length; i++) {
          const section = potentialTeamSections[i];
          // First look for explicit member cards/elements within the section
          const memberSelectors = [
            '.team-member', '.member', '.employee', '.profile-card', '.bio-card',
            '.team-card', '.person', '.staff-member', '.team-item',
            '[class*="team-member"]', '[class*="member-card"]'
          ];
          
          let memberElements = [];
          
          // Try to find member elements inside the section
          for (const selector of memberSelectors) {
            try {
              const elements = section.querySelectorAll(selector);
              if (elements && elements.length > 0) {
                // Convert NodeList to array and add to memberElements
                for (let i = 0; i < elements.length; i++) {
                  memberElements.push(elements[i]);
                }
              }
            } catch (e) {
              // Ignore invalid selectors
            }
          }
          
          // If no explicit member elements found, look for immediate children that might be member cards
          if (memberElements.length === 0) {
            // Look for direct children div/li elements that might be member cards
            const directChildren = section.children;
            
            if (directChildren && directChildren.length > 1 && directChildren.length < 20) {
              // If there are multiple similar direct children, they might be member cards
              // But not too many (to avoid false positives)
              const tagName = directChildren[0].tagName;
              let similarChildrenCount = 0;
              
              for (let i = 0; i < directChildren.length; i++) {
                if (directChildren[i].tagName === tagName) {
                  similarChildrenCount++;
                  memberElements.push(directChildren[i]);
                }
              }
              
              // If not enough similar children, don't use them as member elements
              if (similarChildrenCount < 2) {
                memberElements = [];
              }
            }
          }
          
          // Process found member elements
          for (let j = 0; j < memberElements.length; j++) {
            const memberElement = memberElements[j];
            const teamMember = extractTeamMemberInfo(memberElement);
            
            // Only add if we have at least a name
            if (teamMember.name && teamMember.name.length > 0) {
              // Check if this member already exists (by name)
              let existingMember = null;
              for (let k = 0; k < teamMembers.length; k++) {
                if (teamMembers[k].name === teamMember.name) {
                  existingMember = teamMembers[k];
                  break;
                }
              }
              
              if (!existingMember) {
                teamMembers.push(teamMember);
              } else {
                // If we already have this member, merge in any additional info we found
                if (!existingMember.title && teamMember.title) {
                  existingMember.title = teamMember.title;
                }
                if (!existingMember.bio && teamMember.bio) {
                  existingMember.bio = teamMember.bio;
                }
                if (!existingMember.imageUrl && teamMember.imageUrl) {
                  existingMember.imageUrl = teamMember.imageUrl;
                }
                // Merge contact info
                if (!existingMember.contact.email && teamMember.contact.email) {
                  existingMember.contact.email = teamMember.contact.email;
                }
                if (!existingMember.contact.phone && teamMember.contact.phone) {
                  existingMember.contact.phone = teamMember.contact.phone;
                }
                if (!existingMember.contact.linkedin && teamMember.contact.linkedin) {
                  existingMember.contact.linkedin = teamMember.contact.linkedin;
                }
              }
            }
          }
        }
        
        return teamMembers;
      })()
    `);
    
    return result as TeamMember[];
  } catch (error) {
    console.error("Error in extractTeamInfo:", error);
    return []; // Return empty array in case of error
  }
}

/**
 * Finds potential team pages on a website
 * @param page - Puppeteer page instance
 * @param baseUrl - Base URL of the website
 * @returns Promise containing array of team page URLs
 */
export async function findTeamPages(page: Page, baseUrl: string): Promise<string[]> {
  console.log(`Finding team pages for ${baseUrl}`);

  try {
    // Use string-based evaluation to avoid TypeScript transpilation issues
    const result = await page.evaluate(`
      (function(baseUrl) {
        const teamLinks = [];

        // Team page keywords to look for in links
        const teamPageKeywords = [
          "team",
          "our team",
          "meet the team",
          "people",
          "our people",
          "staff",
          "our staff",
          "leadership",
          "leaders",
          "management",
          "executives",
          "board",
          "directors",
          "our leadership",
          "team members",
          "who we are",
          "about us",
          "meet us",
          "founders"
        ];

        // First look for any links that might point to team pages
        document.querySelectorAll("a").forEach(function(link) {
          const href = link.getAttribute("href");
          const text = link.textContent ? link.textContent.toLowerCase().trim() : "";

          if (!href) return;

          // Check if link text exactly matches a keyword
          let exactTextMatch = false;
          for (let i = 0; i < teamPageKeywords.length; i++) {
            const keyword = teamPageKeywords[i];
            if (text === keyword || text === keyword.charAt(0).toUpperCase() + keyword.slice(1)) {
              exactTextMatch = true;
              break;
            }
          }

          // Check if href contains a team keyword as a distinct segment
          const hrefLower = href.toLowerCase();
          let urlKeywordMatch = false;
          
          for (let i = 0; i < teamPageKeywords.length; i++) {
            const keyword = teamPageKeywords[i];
            const keywordNoSpaces = keyword.replace(/\s+/g, "");
            const keywordWithHyphens = keyword.replace(/\s+/g, "-");

            const urlSegments = hrefLower.split("/").filter(function(segment) { return segment.length > 0; });
            const lastSegment = urlSegments.length > 0 ? urlSegments[urlSegments.length - 1] : "";
            const segmentWithoutExt = lastSegment.replace(/\.[^/.]+$/, "");

            if (segmentWithoutExt === keywordNoSpaces ||
                segmentWithoutExt === keywordWithHyphens ||
                segmentWithoutExt.startsWith(keywordWithHyphens + "-") ||
                segmentWithoutExt.endsWith("-" + keywordWithHyphens)) {
              urlKeywordMatch = true;
              break;
            }
          }

          if (exactTextMatch || urlKeywordMatch) {
            try {
              let fullUrl = href;

              // Handle relative URLs
              if (href.startsWith("/")) {
                const url = new URL(baseUrl);
                fullUrl = url.origin + href;
              } else if (!href.startsWith("http")) {
                fullUrl = baseUrl + (href.startsWith("/") ? "" : "/") + href;
              }

              // Only consider URLs from the same domain
              const linkUrl = new URL(fullUrl);
              const baseUrlObj = new URL(baseUrl);

              if (linkUrl.hostname === baseUrlObj.hostname) {
                teamLinks.push(fullUrl);
              }
            } catch (e) {
              console.log("Error parsing URL: " + href);
              // Skip invalid URLs
            }
          }
        });

        // Sort links by priority
        return teamLinks.sort(function(a, b) {
          // Prioritize URLs with "team" in them
          const aIsTeam = a.toLowerCase().includes("team");
          const bIsTeam = b.toLowerCase().includes("team");

          if (aIsTeam && !bIsTeam) return -1;
          if (!aIsTeam && bIsTeam) return 1;

          // Next priority for "leadership" pages
          const aIsLeadership = a.toLowerCase().includes("leadership");
          const bIsLeadership = b.toLowerCase().includes("leadership");

          if (aIsLeadership && !bIsLeadership) return -1;
          if (!aIsLeadership && bIsLeadership) return 1;

          // Default to shortest URL (likely more relevant)
          return a.length - b.length;
        });
      })("${baseUrl}")
    `);
    
    return result as string[];
  } catch (error) {
    console.error("Error in findTeamPages:", error);
    return []; // Return empty array in case of error
  }
}

/**
 * Determines if team details are complete enough
 * @param teamMembers - Array of team members
 * @returns boolean indicating if team details are complete
 */
export function isTeamInfoComplete(teamMembers: TeamMember[] | undefined): boolean {
  if (!teamMembers || teamMembers.length === 0) {
    return false;
  }

  // Check if at least one team member has a title or bio
  return teamMembers.some(member => Boolean(member.title) || Boolean(member.bio));
}

/**
 * Merges two sets of team members, removing duplicates
 * @param existing - Existing team members
 * @param additional - Additional team members to merge
 * @returns Merged team members array
 */
export function mergeTeamInfo(
  existing: TeamMember[] | undefined,
  additional: TeamMember[] | undefined
): TeamMember[] {
  if (!existing || existing.length === 0) {
    return additional || [];
  }

  if (!additional || additional.length === 0) {
    return existing;
  }

  const existingNames = new Set(existing.map((member) => member.name));
  const newMembers = additional.filter((member) => !existingNames.has(member.name));

  return [...existing, ...newMembers];
}
