import { Page } from 'puppeteer';
import { SummarizationManager } from 'totalads-shared';

// Helper function to clean text for prompt
function cleanTextForPrompt(text: string): string {
  if (!text) return '';
  
  // Remove extra whitespace and newlines
  return text
    .replace(/\s+/g, ' ')
    .replace(/\n+/g, ' ')
    .trim();
}

/**
 * Helper function to extract text between two keywords
 * @param text The text to extract from
 * @param startKeyword The keyword marking the start (case-insensitive)
 * @param endKeyword The keyword marking the end (case-insensitive)
 * @returns The extracted text or null if not found
 */
function extractTextBetween(text: string, startKeyword: string, endKeyword: string): string | null {
  if (!text) return null;
  
  try {
    // Case insensitive search
    const regex = new RegExp(`${startKeyword}[\\s\\S]*?(?=${endKeyword})`, 'i');
    const match = text.match(regex);
    if (match && match[0]) {
      return match[0].replace(new RegExp(startKeyword, 'i'), '').trim();
    }
    return null;
  } catch (error) {
    console.warn('Error extracting text between keywords:', error);
    return null;
  }
}

// Helper function to create a deterministic cache key without using require
function createCacheKey(prefix: string, content: string): string {
  // Simple string-based hash function that works in browser context
  let hash = 0;
  if (content.length === 0) return `${prefix}_0`;
  
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  // Convert to positive hex string and take the first 10 chars
  const hexHash = Math.abs(hash).toString(16).substring(0, 10);
  return `${prefix}_${hexHash}`;
}

import { ContactDetails, CompanyInfo, ScrapedData } from '../../types/scrapper';

// Helper functions for extracting contact information from text

/**
 * Extract email addresses from text
 * @param text Text to extract from
 * @returns Array of email addresses
 */
function extractEmails(text: string): string[] {
  if (!text) return [];
  
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  return [...new Set(text.match(emailRegex) || [])];
}

/**
 * Extract phone numbers from text
 * @param text Text to extract from
 * @returns Array of phone numbers
 */
function extractPhoneNumbers(text: string): string[] {
  if (!text) return [];
  
  // Match various phone number formats
  const phoneRegex = /(?:(?:\+|00)[0-9]{1,3}[\s.-]?)?(?:\([0-9]{1,4}\)[\s.-]?)?(?:[0-9]{1,4}[\s.-]?){2,}/g;
  return [...new Set(text.match(phoneRegex) || [])];
}

/**
 * Extract physical address from text
 * @param text Text to extract from
 * @returns Extracted address or null
 */
function extractAddress(text: string): string | null {
  if (!text) return null;
  
  // Look for address patterns like "123 Main St" or sections labeled as address
  const addressPattern = text.match(/[\w\s]+(street|st\.?|avenue|ave\.?|road|rd\.?|drive|dr\.?|lane|ln\.?|boulevard|blvd\.?|place|pl\.?)[\w\s,]+/i);
  if (addressPattern) return addressPattern[0].trim();
  
  // Look for text after "address:" label
  const addressSection = extractTextBetween(text, 'address[\\s\\:]+', '\\n');
  return addressSection;
}

/**
 * Extract social media links from text
 * @param text Text to extract from
 * @returns Object with social platform names as keys and URLs as values
 */
function extractSocialLinks(text: string): Record<string, string> {
  if (!text) return {};
  
  const socialLinks: Record<string, string> = {};
  
  // Extract URLs for common social platforms
  const platforms = [
    { name: 'linkedin', pattern: /(?:linkedin\.com\/(?:company\/|in\/)?)[\w-]+/i },
    { name: 'twitter', pattern: /(?:twitter\.com\/|x\.com\/)[\w-]+/i },
    { name: 'facebook', pattern: /facebook\.com\/[\w.-]+/i },
    { name: 'instagram', pattern: /instagram\.com\/[\w.-]+/i },
    { name: 'youtube', pattern: /youtube\.com\/(?:channel\/|user\/)?[\w-]+/i },
  ];
  
  for (const platform of platforms) {
    const match = text.match(platform.pattern);
    if (match && match[0]) {
      // Ensure URL has proper protocol
      const url = match[0].includes('http') ? match[0] : `https://${match[0]}`;
      socialLinks[platform.name] = url;
    }
  }
  
  return socialLinks;
}

// Initialize the summarization manager (singleton)
const summManager = new SummarizationManager();

/**
 * Enhance scraped data with AI processing using only two LLM API calls:
 * 1. Extract contact info from page text
 * 2. Summarize company info and find additional data
 * 
 * @param page - Puppeteer page object
 * @param scrapedData - The scraped data from traditional extraction
 * @returns Enhanced data with AI-extracted information
 */
export async function enhanceScrapedDataWithAI(page: Page, scrapedData: ScrapedData): Promise<ScrapedData> {
  console.log("Enhancing scraped data with AI (2-step workflow)");
  
  // Get the cleaned text content from the page
  const pageText = scrapedData.text || '';
  
  // STEP 1: Extract contact information from the full page text
  const enhancedContactInfo = await extractContactDetailsFromText(
    pageText, 
    scrapedData.contactDetails || {}
  );
  console.log('Step 1: Contact extraction complete');
  
  // STEP 2: Process about data and look for additional information
  const enhancedAboutInfo = await processAboutDataWithAI(
    scrapedData.aboutData || {},
    enhancedContactInfo,
    pageText
  );
  console.log('Step 2: About data enhancement complete');
  
  // Return the enhanced data
  return {
    ...scrapedData,
    contactDetails: enhancedContactInfo,
    aboutData: enhancedAboutInfo
  };
}

/**
 * Extract contact details from page text using AI
 * @param pageText - The full text content of the page
 * @param existingContactDetails - Contact details already extracted
 * @returns Enhanced contact details
 */
async function extractContactDetailsFromText(pageText: string, existingContactDetails: ContactDetails = {}): Promise<ContactDetails> {
  try {
    // Prepare text for LLM - clean and trim to reduce tokens
    const cleanedText = cleanTextForPrompt(pageText);

    // Define a prompt that instructs the LLM to extract contact info - optimized for minimal token usage
    const prompt = `Extract ALL contact information from this website text. Return ONLY a JSON object with these fields:
"email":[array of ALL email addresses found],
"phone":[array of ALL phone numbers found with country code if available],
"address":"full physical address if found",
"socialLinks":{platform names mapped to complete URLs for linkedin, facebook, twitter, instagram, youtube, etc}

Analyze this text carefully: ${cleanedText.substring(0, 15000)}`;

    // Create a cache key to avoid unnecessary API calls
    const cacheKey = createCacheKey('contact_extraction', cleanedText.substring(0, 1000));

    // Make a single API call - do not use cacheKey as it's not supported in the API
    const result = await summManager.summarizeDescription({
      text: prompt
    });
    
    if (!result || !result.data) {
      console.warn('No result from AI contact extraction');
      return existingContactDetails;
    }
    
    // Try to parse the result as JSON
    let aiContactData: ContactDetails;
    try {
      // The result might be a JSON string or an object with relevant fields
      const resultText = typeof result.data === 'string' ? result.data : JSON.stringify(result.data);
      
      // First try to find JSON pattern within the text
      const jsonMatch = resultText.match(/\{[\s\S]*\}/); 
      if (jsonMatch) {
        try {
          aiContactData = JSON.parse(jsonMatch[0]);
          console.log('Successfully extracted contact JSON from response');
        } catch (innerError) {
          console.warn('Found JSON-like structure but failed to parse contact data:', innerError);
          // Extract contact details without JSON
          aiContactData = {
            email: extractEmails(resultText),
            phone: extractPhoneNumbers(resultText),
            address: extractAddress(resultText) || undefined,
            socialLinks: extractSocialLinks(resultText)
          };
        }
      } else {
        console.warn('No JSON structure found in contact response');
        // Extract contact details directly from text
        aiContactData = {
          email: extractEmails(resultText),
          phone: extractPhoneNumbers(resultText),
          address: extractAddress(resultText) || undefined,
          socialLinks: extractSocialLinks(resultText)
        };
      }
    } catch (error) {
      console.error('Failed to parse AI contact data:', error);
      return existingContactDetails;
    }
    
    // Merge with existing contact details
    const mergedContact: ContactDetails = {
      name: existingContactDetails.name || undefined,
      email: [
        ...(existingContactDetails.email || []),
        ...(aiContactData.email || [])
      ].filter((v, i, a) => a.indexOf(v) === i), // Remove duplicates
      phone: [
        ...(existingContactDetails.phone || []), 
        ...(aiContactData.phone || [])
      ].filter((v, i, a) => a.indexOf(v) === i), // Remove duplicates
      address: existingContactDetails.address || aiContactData.address,
      socialLinks: {
        ...(existingContactDetails.socialLinks || {}),
        ...(aiContactData.socialLinks || {})
      }
    };

    console.log('AI contact extraction complete');
    return mergedContact;
  } catch (error) {
    console.error("Error in AI contact extraction:", error);
    return existingContactDetails;
  }
}

/**
 * Process about data with AI to summarize and extract additional information
 * 
 * @param existingAboutInfo - Existing about information from traditional extraction
 * @param contactDetails - Contact details for reference
 * @param pageText - The cleaned page text
 * @returns Enhanced company information
 */
async function processAboutDataWithAI(
  existingAboutInfo: CompanyInfo,
  contactDetails: ContactDetails,
  pageText: string
): Promise<CompanyInfo> {
  try {
    // Prepare text - clean and optimize for token usage
    const existingDescription = existingAboutInfo.companyDescription || '';
    const cleanedPageText = cleanTextForPrompt(pageText).substring(0, 10000);

    // Create an optimized prompt for the LLM focusing only on what we need
    const prompt = `Analyze company info and summarize. Return ONLY JSON with:
"companyDescription":"Clear 2-sentence summary about what company does",
"companyValues":[core values mentioned],
"industries":[specific industries they serve],
"teamMembers":[{"name":"Full Name","title":"Position","bio":"Brief description"}],
"additionalContactInfo":{"emails":[any emails not in ${JSON.stringify(contactDetails.email || [])}],"phones":[any phones not in ${JSON.stringify(contactDetails.phone || [])}],"address":"better formatted address"}

Analyze this text: ${existingDescription} ${cleanedPageText}`;

    // Make a single API call
    const result = await summManager.summarizeDescription({
      text: prompt
    });

    if (!result.success || !result.data) {
      console.warn("AI about data processing failed:", result.error);
      return existingAboutInfo;
    }

    // Extract JSON from the response, which might contain text before or after the JSON
    let aiAboutData: any;
    try {
      const resultText = result.data as string;
      
      // Try to find JSON pattern within the text
      const jsonMatch = resultText.match(/\{[\s\S]*\}/); 
      if (jsonMatch) {
        try {
          aiAboutData = JSON.parse(jsonMatch[0]);
          console.log('Successfully extracted JSON from response');
        } catch (innerError) {
          console.warn('Found JSON-like structure but failed to parse:', innerError);
          // Attempt to extract useful information even without JSON
          aiAboutData = {
            companyDescription: extractTextBetween(resultText, 'description', 'mission') || 
                               extractTextBetween(resultText, 'about', 'mission')
          };
        }
      } else {
        // No JSON found, try to extract key information from text format
        console.warn('No JSON structure found in response');
        aiAboutData = {
          companyDescription: resultText.substring(0, 500) // Just use start of response as description
        };
      }
    } catch (e) {
      console.error("Failed to process AI about data response:", e);
      return existingAboutInfo;
    }

    // Merge with existing about info
    const mergedAbout: CompanyInfo = {
      companyDescription: aiAboutData.companyDescription || existingAboutInfo.companyDescription,
      foundingInfo: aiAboutData.foundingInfo || existingAboutInfo.foundingInfo,
      missionStatement: aiAboutData.missionStatement || existingAboutInfo.missionStatement,
      companyValues: [
        ...(existingAboutInfo.companyValues || []),
        ...(aiAboutData.companyValues || [])
      ].filter((v, i, a) => a.indexOf(v) === i), // Remove duplicates
      industries: [
        ...(existingAboutInfo.industries || []),
        ...(aiAboutData.industries || [])
      ].filter((v, i, a) => a.indexOf(v) === i), // Remove duplicates
      awards: existingAboutInfo.awards,
      globalPresence: existingAboutInfo.globalPresence,
      officeLocations: existingAboutInfo.officeLocations,
      certifications: existingAboutInfo.certifications
    };

    // Handle team members if any were found
    if (aiAboutData.teamMembers && aiAboutData.teamMembers.length > 0) {
      const existingTeamNames = new Set(
        (existingAboutInfo.teamMembers || []).map(member => member.name.toLowerCase())
      );

      // Only add team members that weren't already found
      const newTeamMembers = aiAboutData.teamMembers.filter(
        (member: any) => !existingTeamNames.has(member.name.toLowerCase())
      );

      mergedAbout.teamMembers = [
        ...(existingAboutInfo.teamMembers || []),
        ...newTeamMembers
      ];
    }

    // Add any additional contact info to the contact details
    if (aiAboutData.additionalContactInfo) {
      // This would update contactDetails if needed
      // We're not using this here since we're returning the about info only
    }

    return mergedAbout;
  } catch (error) {
    console.error("Error in AI about data processing:", error);
    return existingAboutInfo;
  }
}

// End of file
