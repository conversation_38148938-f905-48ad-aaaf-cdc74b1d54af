import { Page } from "puppeteer";
// Using direct JS wrapper to avoid ESM/CommonJS compatibility issues
import { enhanceAboutData, EnhancedAboutInfo } from "totalads-shared/src/services/ai/aiWrapper.js";
import { AboutInfo } from "totalads-shared/src/services/ai/types";

/**
 * Interface for company about information
 */
// Using the AboutInfo interface from shared package
// Re-exporting it for backward compatibility

// Enhanced about info with AI processed data
// export { EnhancedAboutInfo } from "totalads-shared/src/services/ai";

/**
 * Extracts about information from a webpage
 * @param page - Puppeteer page instance
 * @returns Promise containing company info
 */
export async function extractAboutInfo(
	page: Page,
	baseUrl?: string, // baseUrl is not used in the current implementation but kept for potential future use
): Promise<EnhancedAboutInfo> {
	console.log("Extracting about page information");

	try {
		// Using a string-based function to avoid any TypeScript transpilation issues in the browser context
		const result = await page.evaluate(`
      (function() {
        // Define our structure in plain JavaScript
        const aboutInfo = {
          companyDescription: undefined,
          foundingInfo: undefined,
          missionStatement: undefined,
          companyValues: undefined,
          awards: undefined,
          industries: undefined,
          globalPresence: undefined,
          officeLocations: undefined,
          certifications: undefined
        };

        // Helper function to check if a string contains a pattern
        function stringContains(text, pattern) {
          if (!text) return false;
          return text.toLowerCase().indexOf(pattern.toLowerCase()) !== -1;
        }
        
        // Helper function to check if an element matches a pattern
        function matchesPattern(element, pattern, attribute = 'class') {
          const attributeValue = element.getAttribute(attribute);
          return attributeValue ? stringContains(attributeValue, pattern) : false;
        }

        // Helper function to find elements by text content
        function getElementsByText(tag, text) {
          const elements = document.querySelectorAll(tag);
          return Array.from(elements).filter(function(element) {
            return element.textContent && element.textContent.toLowerCase().includes(text.toLowerCase());
          });
        }

        // Process all the text content from potential about sections to extract company description
        let allDescriptionText = '';
        const potentialAboutSections = document.querySelectorAll('section, div, article');
        potentialAboutSections.forEach(function(section) {
          if (section && section.textContent) {
            const text = section.textContent.trim();
            if (text.length > 50) { // Only consider substantial text
              allDescriptionText += text + ' ';
            }
          }
        });

        // Clean up description text
        if (allDescriptionText) {
          // Remove extra whitespace
          allDescriptionText = allDescriptionText.replace(/\s+/g, ' ').trim();
          
          // Set the company description if it's substantial
          if (allDescriptionText.length > 100) {
            aboutInfo.companyDescription = allDescriptionText;
          }
        }

        // Extract founding info
        try {
          const textElements = document.querySelectorAll('p, div, span, section, article');
          const foundingKeywords = ['founded in', 'established in', 'since', 'founded'];
          
          for (let i = 0; i < textElements.length; i++) {
            const element = textElements[i];
            if (element.textContent) {
              const text = element.textContent.trim().toLowerCase();
              
              for (let j = 0; j < foundingKeywords.length; j++) {
                const keyword = foundingKeywords[j];
                const keywordIndex = text.indexOf(keyword);
                
                if (keywordIndex !== -1) {
                  const textToSearch = text.substring(Math.max(0, keywordIndex - 20), 
                                                 Math.min(text.length, keywordIndex + keyword.length + 20));
                  
                  const yearMatches = textToSearch.match(/\d{4}/g);
                  if (yearMatches && yearMatches.length > 0) {
                    for (let k = 0; k < yearMatches.length; k++) {
                      const year = parseInt(yearMatches[k], 10);
                      if (year > 1900 && year <= new Date().getFullYear()) {
                        aboutInfo.foundingInfo = 'Founded in ' + year;
                        break;
                      }
                    }
                  }
                }
                if (aboutInfo.foundingInfo) break;
              }
              if (aboutInfo.foundingInfo) break;
            }
          }
        } catch (foundingError) {
          // console.error('Error extracting founding info:', foundingError); // Keep console logs minimal in browser context
        }
        
        // Extract mission statement
        try {
          const missionHeadings = getElementsByText('h1, h2, h3, h4, h5, h6', 'mission');
          if (missionHeadings.length > 0) {
            const missionElement = missionHeadings[0].closest('section, div, article') || missionHeadings[0].parentElement;
            if (missionElement && missionElement.textContent) {
              const missionText = missionElement.textContent.replace(/\s+/g, ' ').trim();
              if (missionText.length > 30 && missionText.length < 1000) {
                 aboutInfo.missionStatement = missionText;
              }
            }
          }
        } catch (missionError) {
          // console.error('Error extracting mission statement:', missionError);
        }

        // Extract company values
        try {
          const valuesHeadings = getElementsByText('h1, h2, h3, h4, h5, h6', 'values');
          if (valuesHeadings.length > 0) {
            const valuesSection = valuesHeadings[0].closest('section, div, article') || valuesHeadings[0].parentElement;
            if (valuesSection) {
              const listItems = valuesSection.querySelectorAll('li');
              if (listItems.length > 0) {
                aboutInfo.companyValues = Array.from(listItems).map(function(li) { return li.textContent ? li.textContent.trim() : ''; }).filter(function(value) { return value.length > 2; });
              }
            }
          }
        } catch (valuesError) {
          // console.error('Error extracting company values:', valuesError);
        }

        // Extract awards
        try {
          const awardsHeadings = getElementsByText('h1, h2, h3, h4, h5, h6', 'award'); // also 'recognition', 'accolade'
          if (awardsHeadings.length > 0) {
            const awardsSection = awardsHeadings[0].closest('section, div, article') || awardsHeadings[0].parentElement;
            if (awardsSection) {
              const listItems = awardsSection.querySelectorAll('li');
              if (listItems.length > 0) {
                aboutInfo.awards = Array.from(listItems).map(function(li) { return li.textContent ? li.textContent.trim() : ''; }).filter(function(award) { return award.length > 5; });
              } else {
                // Fallback: look for paragraphs if no list items
                const paragraphs = awardsSection.querySelectorAll('p');
                aboutInfo.awards = Array.from(paragraphs).map(function(p) { return p.textContent ? p.textContent.trim() : ''; }).filter(function(award) { return award.length > 5; });
              }
            }
          }
        } catch (awardsError) {
          // console.error('Error extracting awards:', awardsError);
        }

        // Extract industries
        try {
          const industryKeywords = ['industry', 'industries', 'sector', 'market'];
          let foundIndustries = [];
          for (let i = 0; i < industryKeywords.length; i++) {
            const headings = getElementsByText('h1, h2, h3, h4, h5, h6, p, span', industryKeywords[i]);
            if (headings.length > 0) {
              const section = headings[0].closest('section, div, article') || headings[0].parentElement;
              if (section) {
                const listItems = section.querySelectorAll('li');
                if (listItems.length > 0) {
                  foundIndustries = Array.from(listItems).map(function(li) { return li.textContent ? li.textContent.trim() : ''; });
                  break;
                }
              }
            }
          }
          if (foundIndustries.length > 0) {
            aboutInfo.industries = foundIndustries.filter(function(industry) { return industry.length > 3; });
          }
        } catch (industriesError) {
          // console.error('Error extracting industries:', industriesError);
        }

        // Extract global presence and office locations
        try {
          const locationKeywords = ['location', 'office', 'global', 'presence', 'contact'];
          let officeLocations = [];
          let globalPresence = false;

          for (let i = 0; i < locationKeywords.length; i++) {
            const headings = getElementsByText('h1, h2, h3, h4, h5, h6, p, span, a', locationKeywords[i]);
            if (headings.length > 0) {
              const section = headings[0].closest('section, div, article') || headings[0].parentElement;
              if (section && section.textContent) {
                const textContent = section.textContent.toLowerCase();
                if (stringContains(textContent, 'global') || stringContains(textContent, 'international') || stringContains(textContent, 'worldwide')) {
                  globalPresence = true;
                }
                const listItems = section.querySelectorAll('li, p, address, div.location-item'); // Common tags for locations
                if (listItems.length > 0) {
                   officeLocations = Array.from(listItems).map(function(item) { return item.textContent ? item.textContent.replace(/\s+/g, ' ').trim() : ''; })
                                      .filter(function(loc) { return loc.length > 5 && loc.length < 100; }); // Basic filter for validity
                }
                if (officeLocations.length > 0 || globalPresence) break;
              }
            }
          }
          if (officeLocations.length > 0) aboutInfo.officeLocations = officeLocations;
          if (globalPresence) aboutInfo.globalPresence = globalPresence;
        } catch (locationsError) {
          // console.error('Error extracting locations:', locationsError);
        }

        // Extract certifications (using plain JS, no direct regex literals)
        try {
          const certRegexPatterns = [
            'ISO \\d+', // e.g., ISO 9001
            'Certified [A-Za-z ]+', // e.g., Certified B Corp
            '[A-Za-z]+ Certified'
          ];
          const certificationPatterns = [
            'iso', 'certified', 'accredited', 'certification'
          ];
          let foundCerts = [];
          const allText = document.body.innerText || document.body.textContent || '';

          for (let i = 0; i < certificationPatterns.length; i++) {
            const pattern = certificationPatterns[i];
            let startIndex = 0;
            let matchIndex;
            while ((matchIndex = allText.toLowerCase().indexOf(pattern, startIndex)) !== -1) {
              const surroundingText = allText.substring(Math.max(0, matchIndex - 50), Math.min(allText.length, matchIndex + pattern.length + 50));
              // Try to match more specific patterns now
              for (let j = 0; j < certRegexPatterns.length; j++) {
                const regex = new RegExp(certRegexPatterns[j], 'gi'); // Use RegExp constructor
                let specificMatch;
                while ((specificMatch = regex.exec(surroundingText)) !== null) {
                  if (specificMatch[0].length > 4 && specificMatch[0].length < 100) { // Basic validation
                    if (foundCerts.indexOf(specificMatch[0]) === -1) { // Avoid duplicates
                        foundCerts.push(specificMatch[0]);
                    }
                  }
                }
              }
              startIndex = matchIndex + pattern.length;
            }
          }
          if (foundCerts.length > 0) {
            aboutInfo.certifications = foundCerts;
          }
        } catch (certsError) {
          // console.error('Error extracting certifications:', certsError);
        }

        return aboutInfo;
      })()
    `);

		// Cast the result to AboutInfo type
		const aboutInfo = result as AboutInfo;

		// Now enhance the about info using our AI function
		try {
			console.log("Enhancing about information with AI...");
			const enhancementResult = await enhanceAboutData({
				aboutData: aboutInfo,
				websiteUrl: page.url(),
			});

			if (enhancementResult.success && enhancementResult.data) {
				console.log("Successfully enhanced about information with AI");
				return enhancementResult.data;
			} else if (enhancementResult.error) {
				console.warn(
					"Failed to enhance about info with AI:",
					enhancementResult.error,
				);
			}
		} catch (aiError) {
			console.error(
				"Error during AI enhancement of about info:",
				aiError,
			);
		}

		// Return the original data if AI enhancement fails
		return aboutInfo;
	} catch (error) {
		console.error("Error extracting about info:", error);
		return {} as EnhancedAboutInfo; // Return empty object on error
	}
}
