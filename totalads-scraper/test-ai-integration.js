#!/usr/bin/env node

/**
 * Test script to verify AI integration is working correctly
 * This script tests both the text summarizer and about data enhancer functions
 */

import { summarizeText, enhanceAboutData } from "totalads-shared";

async function testTextSummarizer() {
	console.log("🧪 Testing Text Summarizer...");

	const testText = `
    TechCorp is a leading technology company founded in 2010 that specializes in artificial intelligence and machine learning solutions. 
    The company has grown from a small startup to a major player in the tech industry, serving Fortune 500 companies worldwide.
    Our mission is to democratize AI technology and make it accessible to businesses of all sizes.
    We have offices in San Francisco, New York, London, and Tokyo, with over 500 employees globally.
    TechCorp has received numerous awards including the "Best AI Company 2023" and "Innovation Leader Award".
    Our core values include innovation, transparency, customer success, and ethical AI development.
    The company went public in 2020 and has been consistently profitable since 2018.
  `;

	try {
		const result = await summarizeText({
			text: testText,
			maxLength: 150,
			focusPoints: ["business model", "company values", "achievements"],
		});

		if (result.success) {
			console.log("✅ Text Summarizer Test PASSED");
			console.log("📝 Summary:", result.data.summary);
			console.log("🔑 Key Points:", result.data.keyPoints);
			console.log("💼 Business Info:", result.data.businessRelevance);
			console.log("📊 Tokens Used:", result.tokensUsed);
		} else {
			console.log("❌ Text Summarizer Test FAILED:", result.error);
		}
	} catch (error) {
		console.log("❌ Text Summarizer Test ERROR:", error.message);
	}
}

async function testAboutDataEnhancer() {
	console.log("\n🧪 Testing About Data Enhancer...");

	const testAboutData = {
		companyName: "TechCorp",
		companyDescription:
			"A leading technology company specializing in AI and ML solutions",
		industry: "Technology",
		foundedYear: 2010,
		employees: "500+",
		locations: ["San Francisco", "New York"],
	};

	try {
		const result = await enhanceAboutData({
			aboutData: testAboutData,
			websiteUrl: "https://techcorp.example.com",
			additionalContext: "Company focuses on enterprise AI solutions",
		});

		if (result.success) {
			console.log("✅ About Data Enhancer Test PASSED");
			console.log(
				"🏢 Enhanced Data:",
				JSON.stringify(result.data, null, 2),
			);
			console.log("📊 Tokens Used:", result.tokensUsed);
		} else {
			console.log("❌ About Data Enhancer Test FAILED:", result.error);
		}
	} catch (error) {
		console.log("❌ About Data Enhancer Test ERROR:", error.message);
	}
}

async function runTests() {
	console.log("🚀 Starting AI Integration Tests...\n");

	// Check if GEMINI_API_KEY is set
	if (!process.env.GEMINI_API_KEY) {
		console.log("⚠️  Warning: GEMINI_API_KEY environment variable not set");
		console.log("   Tests will use mock responses or may fail");
		console.log("   Set GEMINI_API_KEY to test with actual Gemini API\n");
	}

	await testTextSummarizer();
	await testAboutDataEnhancer();

	console.log("\n✨ AI Integration Tests Complete!");
}

// Run the tests
runTests().catch(console.error);
