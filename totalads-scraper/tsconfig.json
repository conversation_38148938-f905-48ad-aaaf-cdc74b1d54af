{"compilerOptions": {"module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "target": "ESNext", "sourceMap": true, "lib": ["es6", "dom"], "rootDir": "..", "baseUrl": "..", "typeRoots": ["./types", "./node_modules/@types"], "noEmit": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "allowJs": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "skipLibCheck": true, "paths": {"totalads-shared/*": ["totalads-shared/*"]}}, "ts-node": {"esm": true}}