{"name": "totalads-scraper", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "packageManager": "pnpm@10.6.3", "scripts": {"start:dev:server": "tsx watch ./src/server/index.ts", "start:server": "node dist/server.js", "build:server": "tsc && node esbuild.config.js --build --entryPoint=./src/server/index.ts --outFile=./dist/server.js", "prepare": "husky install"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.16.0", "@types/express": "^4.17.21", "@types/html-to-text": "^9.0.4", "@types/node": "^20.8.0", "esbuild": "^0.19.8", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "globals": "^15.12.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "prettier": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5.3.3", "typescript-eslint": "^8.16.0"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@types/debug": "^4.1.12", "cheerio": "^1.0.0", "debug": "^4.3.4", "dotenv": "^16.3.1", "express": "^4.18.2", "html-to-text": "^9.0.5", "normalize-url": "^8.0.0", "npm-run-all": "^4.1.5", "puppeteer": "^21.5.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "table": "^6.8.1", "totalads-shared": "git+https://<EMAIL>/TotalAds/totalads-shared.git", "ts-node": "^10.9.2", "zod": "^3.22.4"}, "lint-staged": {"**/*": ["eslint . --fix", "prettier --write .", "git add ."]}}